from django.db import models
from django.core.validators import MinValueValidator
from django.utils import timezone
from datetime import timedelta
from django.db.models.signals import post_save
from django.dispatch import receiver

# Create your models here.
class MachineSystem(models.Model):
    name = models.CharField(max_length=100, verbose_name="System Name")

    class Meta:
        ordering = ['name']
        verbose_name = "Machine System"
        verbose_name_plural = "Machine Systems"

    def __str__(self):
        return self.name

class Machine(models.Model):
    name = models.CharField(max_length=100, verbose_name="Machine Name")
    system = models.ForeignKey(MachineSystem, related_name='machines', on_delete=models.CASCADE, verbose_name="Machine System")
    serial_number = models.CharField(max_length=100, blank=True, null=True, verbose_name="Serial Number")
    tag_number = models.CharField(max_length=100, unique=True, verbose_name="Tag Number")
    description = models.TextField(verbose_name="Description")
    location = models.Char<PERSON>ield(max_length=200, verbose_name="Location")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']
        verbose_name = "Machine"
        verbose_name_plural = "Machines"

    def __str__(self):
        return f"{self.name} (Tag #: {self.tag_number})"


class Part(models.Model):
    part_no = models.CharField(max_length=100, verbose_name="Part Number")
    description = models.CharField(verbose_name="Description")
    minimum_stock = models.IntegerField(
        validators=[MinValueValidator(0)],
        verbose_name="Minimum Stock Level",
        help_text="Minimum quantity that should be kept in stock"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['part_no']
        verbose_name = "Part"
        verbose_name_plural = "Parts"

    def __str__(self):
        return f"{self.part_no} ({self.description})"

    @property
    def current_stock(self):
        """Calculate current stock based on orders, adjustments, minus consumption"""
        total_ordered = sum(order.quantity for order in self.part_orders.all())
        total_consumed = sum(record.number_of_parts_replaced for record in self.get_service_records())
        total_adjustments = sum(adj.signed_quantity for adj in self.inventory_adjustments.all())
        return total_ordered + total_adjustments - total_consumed

    @property
    def stock_status(self):
        """Return stock status: 'low', 'adequate', 'overstocked'"""
        current = self.current_stock
        if current < self.minimum_stock:
            return 'low'
        elif current < self.minimum_stock * 2:
            return 'adequate'
        else:
            return 'overstocked'

    def get_service_records(self):
        """Get all service records for this part across all machines"""
        from service.models import ServiceRecord
        return ServiceRecord.objects.filter(machine_part__part=self)

class OrderDocument(models.Model):
    """Document model for purchase orders, invoices, receipts, etc."""
    document_name = models.CharField(max_length=200, verbose_name="Document Name")
    document_file = models.FileField(
        upload_to='order_documents/',
        verbose_name="Document File",
        help_text="Upload purchase order, invoice, or receipt"
    )
    document_type = models.CharField(
        max_length=50,
        choices=[
            ('purchase_order', 'Purchase Order'),
            ('invoice', 'Invoice'),
            ('receipt', 'Receipt'),
            ('other', 'Other'),
        ],
        default='purchase_order',
        verbose_name="Document Type"
    )
    upload_date = models.DateTimeField(auto_now_add=True)
    notes = models.TextField(blank=True, verbose_name="Notes")

    class Meta:
        ordering = ['-upload_date']
        verbose_name = "Order Document"
        verbose_name_plural = "Order Documents"

    def __str__(self):
        return f"{self.document_name} ({self.get_document_type_display()})"


class PartOrder(models.Model):
    part = models.ForeignKey(Part, related_name='part_orders', on_delete=models.CASCADE, verbose_name="Part")
    quantity = models.IntegerField(
        validators=[MinValueValidator(1)],
        verbose_name="Quantity Ordered"
    )
    order_date = models.DateField(verbose_name="Order Date")
    order_reference_document = models.ForeignKey(
        OrderDocument,
        on_delete=models.SET_NULL,
        blank=True,
        null=True,
        related_name='part_orders',
        verbose_name="Reference Document",
        help_text="Link to purchase order or receipt document"
    )
    unit_cost = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name="Unit Cost",
        help_text="Cost per unit (optional)"
    )
    supplier = models.CharField(
        max_length=200,
        blank=True,
        verbose_name="Supplier",
        help_text="Supplier or vendor name"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-order_date']
        verbose_name = "Part Order"
        verbose_name_plural = "Part Orders"

    def __str__(self):
        return f"{self.part.part_no} - {self.quantity} units ({self.order_date})"

    @property
    def total_cost(self):
        """Calculate total cost if unit cost is available"""
        if self.unit_cost:
            return self.quantity * self.unit_cost
        return None


class InventoryAdjustment(models.Model):
    """Model for tracking inventory adjustments (additions/deductions)"""
    ADJUSTMENT_TYPES = [
        ('addition', 'Addition'),
        ('deduction', 'Deduction'),
    ]

    REASON_CHOICES = [
        ('damage', 'Damage'),
        ('theft', 'Theft'),
        ('requisition', 'Requisition'),
        ('found', 'Found/Discovered'),
        ('correction', 'Inventory Correction'),
        ('expired', 'Expired/Obsolete'),
        ('other', 'Other'),
    ]

    part = models.ForeignKey(
        Part,
        related_name='inventory_adjustments',
        on_delete=models.CASCADE,
        verbose_name="Part"
    )
    adjustment_type = models.CharField(
        max_length=20,
        choices=ADJUSTMENT_TYPES,
        verbose_name="Adjustment Type"
    )
    quantity = models.IntegerField(
        validators=[MinValueValidator(1)],
        verbose_name="Quantity"
    )
    reason = models.CharField(
        max_length=50,
        choices=REASON_CHOICES,
        verbose_name="Reason"
    )
    description = models.TextField(
        verbose_name="Description",
        help_text="Detailed explanation of the adjustment"
    )
    adjustment_date = models.DateField(
        default=timezone.now,
        verbose_name="Adjustment Date"
    )
    created_by = models.CharField(
        max_length=100,
        verbose_name="Created By",
        help_text="Name of person making the adjustment"
    )
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-adjustment_date', '-created_at']
        verbose_name = "Inventory Adjustment"
        verbose_name_plural = "Inventory Adjustments"

    def __str__(self):
        sign = '+' if self.adjustment_type == 'addition' else '-'
        return f"{self.part.part_no}: {sign}{self.quantity} ({self.get_reason_display()})"

    @property
    def signed_quantity(self):
        """Return quantity with appropriate sign"""
        return self.quantity if self.adjustment_type == 'addition' else -self.quantity

class MachinePart(models.Model):
    FREQUENCY_UNITS = [
        ('days', 'Days'),
        ('weeks', 'Weeks'),
        ('months', 'Months'),
    ]
    machine = models.ForeignKey(Machine, related_name='machine_parts', on_delete=models.CASCADE, verbose_name="Machine")
    part = models.ForeignKey(Part, related_name='part_machines', on_delete=models.CASCADE, verbose_name="Part")
    running_hours = models.IntegerField(
        validators=[MinValueValidator(0)],
        verbose_name="Running Hours"
    )
    service_frequency = models.IntegerField(
        validators=[MinValueValidator(1)],
        verbose_name="Service Frequency"
    )
    service_frequency_unit = models.CharField(
        max_length=10,
        choices=FREQUENCY_UNITS,
        verbose_name="Frequency Unit"
    )
    service_instructions = models.TextField(verbose_name="Service Instructions")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['machine', 'part']
        ordering = ['machine__name', 'part__part_no']
        verbose_name = "Machine Part"
        verbose_name_plural = "Machine Parts"

    def __str__(self):
        return f"{self.machine.name} - {self.part}"

    def get_frequency_in_days(self):
        """Convert service frequency to days for calculations"""
        if self.service_frequency_unit == 'days':
            return self.service_frequency
        elif self.service_frequency_unit == 'weeks':
            return self.service_frequency * 7
        elif self.service_frequency_unit == 'months':
            return self.service_frequency * 30  # Approximate
        return 0

    def get_next_service_date(self):
        """Calculate next service date based on last service record"""
        last_service = self.service_records.order_by('-service_date').first()
        frequency_days = self.get_frequency_in_days()
        if last_service:
            return last_service.service_date + timedelta(days=frequency_days)
        return self.created_at.date() + timedelta(days=frequency_days) # If no service history, set service schedule

    def is_overdue(self):
        """Check if service is overdue"""
        next_service = self.get_next_service_date()
        return timezone.now().date() > next_service


# Signal handlers
@receiver(post_save, sender=MachinePart)
def create_initial_service_schedule(sender, instance, created, **kwargs):
    """
    Automatically create a ServiceSchedule when a new MachinePart is added.
    """
    if created:  # Only for newly created MachinePart instances
        from service.models import ServiceSchedule

        # Calculate the initial service date
        next_service_date = instance.get_next_service_date()

        # Create the service schedule
        ServiceSchedule.objects.create(
            machine_part=instance,
            scheduled_date=next_service_date,
            # assigned_technician is left as None - can be assigned later
        )
    