"""
URL patterns for service management.
"""
from django.urls import path
from . import views

app_name = 'service'

urlpatterns = [
    # Service Schedule URLs
    path('schedule/', views.service_schedule_list, name='schedule_list'),
    path('schedule/create/', views.service_schedule_create, name='schedule_create'),
    path('schedule/<int:pk>/edit/', views.service_schedule_edit, name='schedule_edit'),
    
    # Service Ticket URLs
    path('tickets/', views.service_ticket_list, name='ticket_list'),
    path('tickets/<int:pk>/', views.service_ticket_detail, name='ticket_detail'),
    path('tickets/<int:pk>/pdf/', views.service_ticket_pdf, name='ticket_pdf'),
    path('tickets/create/', views.service_ticket_create, name='ticket_create'),
    path('tickets/<int:ticket_pk>/add-schedule/<int:schedule_pk>/', views.add_schedule_to_ticket, name='add_schedule_to_ticket'),
    path('tickets/<int:ticket_pk>/add-item/', views.add_manual_item_to_ticket, name='add_manual_item_to_ticket'),
    path('tickets/<int:ticket_pk>/edit-item/<int:item_pk>/', views.edit_ticket_item, name='edit_ticket_item'),
    path('tickets/<int:ticket_pk>/remove-item/<int:item_pk>/', views.remove_ticket_item, name='remove_ticket_item'),

    # AJAX endpoints
    path('ajax/machine-parts/', views.get_machine_parts_ajax, name='get_machine_parts_ajax'),
    
    # Service Record URLs
    path('records/', views.service_record_list, name='record_list'),
    path('records/<int:pk>/', views.service_record_detail, name='record_detail'),
    path('records/create/', views.service_record_create, name='record_create'),
    path('records/<int:pk>/edit/', views.service_record_edit, name='record_edit'),
]
