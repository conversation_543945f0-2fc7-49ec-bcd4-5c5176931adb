{% extends 'base/base.html' %}
{% load static %}

{% block title %}Service Ticket #{{ ticket.pk }} - {{ block.super }}{% endblock %}

{% block page_header %}
<div class="header">
    <h1 class="header-title">
        Service Ticket #{{ ticket.pk }}
        {% if ticket.service_date < today %}
            {% if ticket.ticket_items.exists %}
                <span class="badge bg-success ms-2">Completed</span>
            {% else %}
                <span class="badge bg-warning ms-2">In Progress</span>
            {% endif %}
        {% elif ticket.service_date == today %}
            <span class="badge bg-info ms-2">Due Today</span>
        {% else %}
            <span class="badge bg-secondary ms-2">Scheduled</span>
        {% endif %}
    </h1>
    <p class="header-subtitle">Service ticket details and work items.</p>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <!-- Ticket Information -->
    <div class="col-12 col-lg-8">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title mb-0">Ticket Information</h5>
                    </div>
                    <div class="col-auto">
                        <div class="btn-group" role="group">
                            <a href="{% url 'service:ticket_pdf' ticket.pk %}" class="btn btn-outline-primary btn-sm" target="_blank">
                                <i class="fas fa-print me-1"></i> Print Worksheet
                            </a>
                            <button type="button" class="btn btn-outline-secondary btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                                <i class="fas fa-cog"></i>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{% url 'service:record_create' %}?ticket={{ ticket.pk }}">
                                    <i class="fas fa-wrench me-2"></i>Add Service Record
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-danger" href="#">
                                    <i class="fas fa-trash me-2"></i>Delete Ticket
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Ticket #:</dt>
                            <dd class="col-sm-7"><strong>#{{ ticket.pk }}</strong></dd>
                            
                            <dt class="col-sm-5">Service Date:</dt>
                            <dd class="col-sm-7">{{ ticket.service_date|date:"M d, Y" }}</dd>
                            
                            <dt class="col-sm-5">Assigned To:</dt>
                            <dd class="col-sm-7">
                                {% if ticket.assigned_technician %}
                                    <a href="{% url 'technicians:detail' ticket.assigned_technician.pk %}">
                                        {{ ticket.assigned_technician.name }}
                                    </a>
                                {% else %}
                                    <span class="text-muted">Unassigned</span>
                                {% endif %}
                            </dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-5">Created:</dt>
                            <dd class="col-sm-7">{{ ticket.created_at|date:"M d, Y" }}</dd>
                            
                            <dt class="col-sm-5">Last Updated:</dt>
                            <dd class="col-sm-7">{{ ticket.updated_at|date:"M d, Y" }}</dd>
                            
                            <dt class="col-sm-5">Items:</dt>
                            <dd class="col-sm-7">
                                <span class="badge bg-primary">{{ ticket_items.count }}</span>
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Quick Actions -->
    <div class="col-12 col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'service:record_create' %}?ticket={{ ticket.pk }}" class="btn btn-primary">
                        <i class="fas fa-wrench me-2"></i>Add Service Record
                    </a>
                    <a href="{% url 'service:ticket_pdf' ticket.pk %}" class="btn btn-outline-primary" target="_blank">
                        <i class="fas fa-print me-2"></i>Print Worksheet
                    </a>
                    <a href="{% url 'service:ticket_list' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Tickets
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Ticket Items and Schedules -->
<div class="row mt-4">
    <!-- Left Column: Service Items -->
    <div class="col-12 col-lg-8">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title mb-0">Service Items ({{ ticket_items.count }})</h5>
                    </div>
                    <div class="col-auto">
                        <button class="btn btn-outline-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addItemModal">
                            <i class="fas fa-plus me-1"></i> Add Item
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                {% if ticket_items %}
                <div class="table-responsive">
                    <table class="table table-hover table-sm">
                        <thead>
                            <tr>
                                <th>Machine</th>
                                <th>Part</th>
                                <th>Source</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for item in ticket_items %}
                            <tr>
                                <td>
                                    <a href="{% url 'machines:detail' item.machine_part.machine.pk %}">
                                        {{ item.machine_part.machine.name }}
                                    </a>
                                    <br><small class="text-muted">{{ item.machine_part.machine.tag_number }}</small>
                                </td>
                                <td>
                                    {{ item.machine_part.part }}
                                    <br><small class="text-muted">{{ item.machine_part.service_instructions|truncatechars:30 }}</small>
                                </td>
                                <td>
                                    {% if item.referenced_schedule %}
                                        <span class="badge bg-info">Scheduled</span>
                                        <br><small>{{ item.referenced_schedule.scheduled_date|date:"M d" }}</small>
                                    {% else %}
                                        <span class="badge bg-secondary">Manual</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if item.service_records.exists %}
                                        <span class="badge bg-success">Completed</span>
                                    {% else %}
                                        <span class="badge bg-warning">Pending</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        {% if not item.service_records.exists %}
                                            <a href="{% url 'service:record_create' %}?ticket_item={{ item.pk }}" class="btn btn-sm btn-primary" title="Add Service Record">
                                                <i class="fas fa-wrench"></i>
                                            </a>
                                            <button type="button" class="btn btn-sm btn-outline-secondary"
                                                    onclick="openEditModal({{ item.pk }}, '{{ item.machine_part.machine.name|escapejs }}', {{ item.machine_part.machine.pk }}, '{{ item.machine_part.part.part_no|escapejs }} - {{ item.machine_part.part.description|escapejs }}', {{ item.machine_part.pk }})"
                                                    title="Edit Item">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger"
                                                    onclick="openRemoveModal({{ item.pk }}, '{{ item.machine_part.machine.name|escapejs }}', '{{ item.machine_part.part.part_no|escapejs }} - {{ item.machine_part.part.description|escapejs }}')"
                                                    title="Remove Item">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        {% else %}
                                            <a href="{% url 'service:record_detail' item.service_records.first.pk %}" class="btn btn-sm btn-outline-success" title="View Service Record">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-list fa-3x mb-3"></i>
                    <p>No items added to this ticket yet.</p>
                    <p class="small">Add items manually or select from scheduled services.</p>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addItemModal">
                        <i class="fas fa-plus me-1"></i> Add First Item
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Right Column: Available Schedules -->
    <div class="col-12 col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-alt me-2"></i>
                    Available Schedules ({{ available_schedules|length }})
                </h5>
            </div>
            <div class="card-body">
                {% if available_schedules %}
                <div class="schedule-list" style="max-height: 500px; overflow-y: auto;">
                    {% for schedule in available_schedules %}
                    <div class="d-flex align-items-center justify-content-between p-2 border-bottom">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-1">
                                <strong class="me-2">{{ schedule.machine_part.machine.name }}</strong>
                                {% if schedule.status == 'overdue' %}
                                    <span class="badge bg-danger">Overdue</span>
                                {% elif schedule.status == 'due_today' %}
                                    <span class="badge bg-warning">Due Today</span>
                                {% else %}
                                    <span class="badge bg-info">{{ schedule.scheduled_date|date:"M d" }}</span>
                                {% endif %}
                            </div>
                            <div class="small text-muted">
                                <i class="fas fa-cog me-1"></i>{{ schedule.machine_part.part.part_no }} - {{ schedule.machine_part.part.description|truncatechars:40 }}
                            </div>
                            <div class="small text-muted">
                                {% if schedule.assigned_technician %}
                                    <i class="fas fa-user me-1"></i>{{ schedule.assigned_technician.name }}
                                    {% if schedule.assigned_technician == ticket.assigned_technician %}
                                        <span class="badge bg-success ms-1">Same Tech</span>
                                    {% endif %}
                                {% else %}
                                    <i class="fas fa-user-slash me-1"></i>Unassigned
                                {% endif %}
                            </div>
                        </div>
                        <div class="ms-2">
                            <a href="{% url 'service:add_schedule_to_ticket' ticket.pk schedule.pk %}"
                               class="btn btn-sm btn-outline-primary"
                               title="Add to ticket">
                                <i class="fas fa-plus"></i>
                            </a>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center text-muted py-4">
                    <i class="fas fa-calendar-times fa-3x mb-3"></i>
                    <p>No available scheduled services.</p>
                    <p class="small">All scheduled items are either already on this ticket or completed.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Add Item Modal -->
<div class="modal fade" id="addItemModal" tabindex="-1" aria-labelledby="addItemModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addItemModalLabel">Add Item to Ticket</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'service:add_manual_item_to_ticket' ticket.pk %}">
                <div class="modal-body">
                    {% csrf_token %}

                    <div class="mb-3">
                        <label for="id_machine" class="form-label">{{ add_item_form.machine.label }}</label>
                        {{ add_item_form.machine }}
                        {% if add_item_form.machine.help_text %}
                            <div class="form-text">{{ add_item_form.machine.help_text }}</div>
                        {% endif %}
                    </div>

                    <div class="mb-3">
                        <label for="id_machine_part" class="form-label">{{ add_item_form.machine_part.label }}</label>
                        {{ add_item_form.machine_part }}
                        <div class="form-text">Select a machine first to see available parts</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Item</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Item Modal -->
<div class="modal fade" id="editItemModal" tabindex="-1" aria-labelledby="editItemModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editItemModalLabel">Edit Ticket Item</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" id="editItemForm">
                <div class="modal-body">
                    {% csrf_token %}

                    <div class="mb-3">
                        <label for="edit_machine" class="form-label">Machine</label>
                        <select class="form-select" id="edit_machine" name="machine">
                            <option value="">Select a machine</option>
                            {% for machine in add_item_form.machine.field.queryset %}
                                <option value="{{ machine.pk }}">{{ machine.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="edit_machine_part" class="form-label">Machine Part</label>
                        <select class="form-select" id="edit_machine_part" name="machine_part" disabled>
                            <option value="">Select a machine first</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Item</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Remove Item Modal -->
<div class="modal fade" id="removeItemModal" tabindex="-1" aria-labelledby="removeItemModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="removeItemModalLabel">Remove Ticket Item</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" id="removeItemForm">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Are you sure you want to remove this item?</strong>
                    </div>
                    <p>You are about to remove the following item from this ticket:</p>
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title mb-1" id="removeItemMachine">Machine Name</h6>
                            <p class="card-text text-muted mb-0" id="removeItemPart">Part Details</p>
                        </div>
                    </div>
                    <p class="mt-3 text-muted small">
                        <i class="fas fa-info-circle me-1"></i>
                        If this item was added from a schedule, it will become available again in the scheduled items list.
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Remove Item</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const machineSelect = document.getElementById('id_machine');
    const machinePartSelect = document.getElementById('id_machine_part');
    const editMachineSelect = document.getElementById('edit_machine');
    const editMachinePartSelect = document.getElementById('edit_machine_part');

    // Function to load machine parts for a given select element
    function loadMachineParts(machineId, targetSelect, selectedPartId = null) {
        if (machineId) {
            targetSelect.disabled = false;

            fetch(`{% url 'service:get_machine_parts_ajax' %}?machine_id=${machineId}`)
                .then(response => response.json())
                .then(data => {
                    targetSelect.innerHTML = '<option value="">Select a machine part</option>';

                    data.results.forEach(function(item) {
                        const option = document.createElement('option');
                        option.value = item.id;
                        option.textContent = item.text;
                        if (selectedPartId && item.id == selectedPartId) {
                            option.selected = true;
                        }
                        targetSelect.appendChild(option);
                    });
                })
                .catch(error => {
                    console.error('Error fetching machine parts:', error);
                    targetSelect.innerHTML = '<option value="">Error loading parts</option>';
                });
        } else {
            targetSelect.disabled = true;
            targetSelect.innerHTML = '<option value="">Select a machine first</option>';
        }
    }

    // Handle machine selection change for add modal
    machineSelect.addEventListener('change', function() {
        loadMachineParts(this.value, machinePartSelect);
    });

    // Handle machine selection change for edit modal
    editMachineSelect.addEventListener('change', function() {
        loadMachineParts(this.value, editMachinePartSelect);
    });

    // Reset add form when modal is closed
    document.getElementById('addItemModal').addEventListener('hidden.bs.modal', function() {
        machineSelect.value = '';
        machinePartSelect.disabled = true;
        machinePartSelect.innerHTML = '<option value="">Select a machine first</option>';
    });

    // Reset edit form when modal is closed
    document.getElementById('editItemModal').addEventListener('hidden.bs.modal', function() {
        editMachineSelect.value = '';
        editMachinePartSelect.disabled = true;
        editMachinePartSelect.innerHTML = '<option value="">Select a machine first</option>';
    });
});

// Global functions for opening modals
function openEditModal(itemId, machineName, machineId, partName, partId) {
    // Set form action
    document.getElementById('editItemForm').action = `{% url 'service:edit_ticket_item' ticket.pk 0 %}`.replace('0', itemId);

    // Set the selected machine
    document.getElementById('edit_machine').value = machineId;

    // Load parts for the selected machine
    if (machineId) {
        loadMachineParts(machineId, document.getElementById('edit_machine_part'), partId);
    }

    // Show modal
    new bootstrap.Modal(document.getElementById('editItemModal')).show();
}

function openRemoveModal(itemId, machineName, partName) {
    // Set form action
    document.getElementById('removeItemForm').action = `{% url 'service:remove_ticket_item' ticket.pk 0 %}`.replace('0', itemId);

    // Set item details
    document.getElementById('removeItemMachine').textContent = machineName;
    document.getElementById('removeItemPart').textContent = partName;

    // Show modal
    new bootstrap.Modal(document.getElementById('removeItemModal')).show();
}

// Helper function for loading machine parts (accessible globally)
function loadMachineParts(machineId, targetSelect, selectedPartId = null) {
    if (machineId) {
        targetSelect.disabled = false;

        fetch(`{% url 'service:get_machine_parts_ajax' %}?machine_id=${machineId}`)
            .then(response => response.json())
            .then(data => {
                targetSelect.innerHTML = '<option value="">Select a machine part</option>';

                data.results.forEach(function(item) {
                    const option = document.createElement('option');
                    option.value = item.id;
                    option.textContent = item.text;
                    if (selectedPartId && item.id == selectedPartId) {
                        option.selected = true;
                    }
                    targetSelect.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Error fetching machine parts:', error);
                targetSelect.innerHTML = '<option value="">Error loading parts</option>';
            });
    } else {
        targetSelect.disabled = true;
        targetSelect.innerHTML = '<option value="">Select a machine first</option>';
    }
}
</script>
{% endblock %}
