{"cells": [{"cell_type": "code", "execution_count": 1, "id": "aaa1686b", "metadata": {}, "outputs": [], "source": ["import os, sys\n", "os.environ.setdefault(\"DJANGO_SETTINGS_MODULE\", \"machine_mantenance.settings\")\n", "os.environ[\"DJANGO_ALLOW_ASYNC_UNSAFE\"] = \"true\"\n", "import django\n", "django.setup()"]}, {"cell_type": "code", "execution_count": null, "id": "c29c329a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "dj<PERSON>v", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 5}