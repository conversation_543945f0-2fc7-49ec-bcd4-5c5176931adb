{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }} - {{ block.super }}{% endblock %}

{% block page_header %}
<div class="header">
    <h1 class="header-title">{{ title }}</h1>
    <p class="header-subtitle">
        {% if system %}
            Update machine system information and settings.
        {% else %}
            Create a new machine system to organize related machines.
        {% endif %}
    </p>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12 col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">System Information</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {{ form.non_field_errors }}
                        </div>
                    {% endif %}
                    
                    <div class="mb-3">
                        <label class="form-label">{{ form.name.label }}</label>
                        {{ form.name }}
                        {% if form.name.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.name.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    {% if system and form.created_at %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ form.created_at.label }}</label>
                                {{ form.created_at }}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">{{ form.updated_at.label }}</label>
                                {{ form.updated_at }}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="d-flex justify-content-end">
                        <a href="{% if system %}{% url 'systems:detail' system.pk %}{% else %}{% url 'systems:list' %}{% endif %}" class="btn btn-secondary me-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">{{ submit_text }}</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Help Card -->
    <div class="col-12 col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    System Organization
                </h5>
            </div>
            <div class="card-body">
                <small class="text-muted">
                    <p><strong>Machine Systems</strong> help organize related machines into logical groups for better management.</p>
                    
                    <p><strong>Examples:</strong></p>
                    <ul>
                        <li>Production Line A</li>
                        <li>Packaging Equipment</li>
                        <li>HVAC Systems</li>
                        <li>Quality Control</li>
                        <li>Material Handling</li>
                    </ul>
                    
                    <hr>
                    
                    <p><strong>Benefits:</strong></p>
                    <ul>
                        <li>Easier machine navigation</li>
                        <li>Grouped maintenance scheduling</li>
                        <li>System-level reporting</li>
                        <li>Better organization</li>
                    </ul>
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}
