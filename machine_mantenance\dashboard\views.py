from django.shortcuts import render
from django.utils import timezone
from datetime import timedelta
from machines.models import Part, Machine, MachinePart
from service.models import ServiceSchedule, ServiceTicket, ServiceRecord


def home(request):
    """Main dashboard with maintenance-focused data"""

    # Get upcoming service schedules (next 30 days)
    upcoming_schedules = ServiceSchedule.objects.filter(
        scheduled_date__gte=timezone.now().date(),
        scheduled_date__lte=timezone.now().date() + timedelta(days=30),
        is_completed=False
    ).select_related('machine_part__machine', 'machine_part__part', 'assigned_technician').order_by('scheduled_date')[:10]

    # Get parts below stock limit
    low_stock_parts = []
    for part in Part.objects.all():
        if part.stock_status == 'low':
            low_stock_parts.append(part)

    # Get upcoming service tickets (next 14 days)
    upcoming_tickets = ServiceTicket.objects.filter(
        service_date__gte=timezone.now().date(),
        service_date__lte=timezone.now().date() + timedelta(days=14)
    ).select_related('machine', 'assigned_technician').order_by('service_date')[:10]

    # Get 5 latest service records
    recent_service_records = ServiceRecord.objects.select_related(
        'machine_part__machine', 'machine_part__part', 'technician'
    ).order_by('-service_date')[:5]

    # Summary statistics
    total_machines = Machine.objects.count()
    total_parts = Part.objects.count()
    overdue_schedules = ServiceSchedule.objects.filter(
        scheduled_date__lt=timezone.now().date(),
        is_completed=False
    ).count()

    context = {
        'upcoming_schedules': upcoming_schedules,
        'low_stock_parts': low_stock_parts[:10],  # Limit to 10 for display
        'upcoming_tickets': upcoming_tickets,
        'recent_service_records': recent_service_records,
        'total_machines': total_machines,
        'total_parts': total_parts,
        'low_stock_count': len(low_stock_parts),
        'overdue_schedules': overdue_schedules,
        'today': timezone.now().date(),
    }

    return render(request, 'pages/dashboard/home.html', context)