from django.db import models
from django.utils import timezone
from django.db.models.signals import post_save
from django.dispatch import receiver

class Technician(models.Model):
    name = models.CharField(max_length=100, verbose_name="Full Name")
    email = models.EmailField(verbose_name="Email Address")
    phone = models.CharField(max_length=20, verbose_name="Phone Number")
    specialization = models.Char<PERSON><PERSON>(max_length=200, verbose_name="Specialization/Skills")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']
        verbose_name = "Technician"
        verbose_name_plural = "Technicians"

    def __str__(self):
        return self.name

class ServiceSchedule(models.Model):
    machine_part = models.ForeignKey(
        'machines.MachinePart',
        related_name='service_schedules',
        on_delete=models.CASCADE,
        verbose_name="Machine Part"
    )
    assigned_technician = models.Foreign<PERSON><PERSON>(
        Technician,
        on_delete=models.CASCADE,
        related_name='service_schedules',
        null=True,
        blank=True,
        verbose_name="Assigned Technician"
    )
    scheduled_date = models.DateField(verbose_name="Scheduled Date")
    is_completed = models.BooleanField(
        default=False,
        verbose_name="Is Completed",
        help_text="Automatically set to True when associated service record is marked as complete"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['scheduled_date']
        verbose_name = "Service Schedule"
        verbose_name_plural = "Service Schedules"

    def __str__(self):
        return f"Service Schedule for {self.machine_part} on {self.scheduled_date}"

    @property
    def is_overdue(self):
        """Check if scheduled service is overdue"""
        return self.scheduled_date < timezone.now().date()

    @property
    def status(self):
        """Return status: 'completed', 'overdue', 'due_today', 'upcoming'"""
        if self.is_completed:
            return 'completed'

        today = timezone.now().date()
        if self.scheduled_date < today:
            return 'overdue'
        elif self.scheduled_date == today:
            return 'due_today'
        else:
            return 'upcoming'

class ServiceTicket(models.Model):
    service_date = models.DateField(verbose_name="Service Date")
    assigned_technician = models.ForeignKey(
        Technician,
        on_delete=models.CASCADE,
        related_name='service_tickets',
        null=True,
        blank=True,
        verbose_name="Assigned Technician"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-service_date']
        verbose_name = "Service Ticket"
        verbose_name_plural = "Service Tickets"

    def __str__(self):
        return f"Service Ticket #{self.pk} - {self.service_date}"

class ServiceTicketItem(models.Model):
    service_ticket = models.ForeignKey(
        ServiceTicket,
        on_delete=models.CASCADE,
        related_name='ticket_items',
        verbose_name="Service Ticket"
    )
    machine_part = models.ForeignKey(
        'machines.MachinePart',
        on_delete=models.CASCADE,
        related_name='ticketed_services',
        verbose_name="Machine Part"
    )
    referenced_schedule = models.ForeignKey(
        ServiceSchedule,
        on_delete=models.CASCADE,
        related_name='items_scheduled',
        null=True,
        blank=True,
        verbose_name="Referenced Schedule"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ['service_ticket', 'machine_part']
        verbose_name = "Service Ticket Item"
        verbose_name_plural = "Service Ticket Items"

    def __str__(self):
        return f"Service Ticket Item for {self.machine_part}"

class ServiceRecord(models.Model):
    STATUS_CHOICES = [
        ('incomplete', 'Incomplete'),
        ('inadequate', 'Inadequate'),
        ('completed', 'Completed'),
        ('needs_review', 'Needs Review'),
    ]

    ticket_item = models.ForeignKey(
        ServiceTicketItem,
        on_delete=models.CASCADE,
        related_name='service_records',
        blank=True,
        null=True,
        verbose_name="Service Ticket Item",
        help_text="Link to service ticket item if this was a scheduled service"
    )
    machine_part = models.ForeignKey(
        'machines.MachinePart',
        on_delete=models.CASCADE,
        related_name='service_records',
        verbose_name="Machine Part"
    )
    service_date = models.DateField(verbose_name="Service Date")
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default='incomplete',
        verbose_name="Service Status",
        help_text="Status of the service work performed"
    )
    number_of_parts_replaced = models.PositiveIntegerField(
        default=0,
        verbose_name="Number of Parts Replaced"
    )
    technician = models.ForeignKey(
        Technician,
        on_delete=models.CASCADE,
        related_name='service_records',
        verbose_name="Technician"
    )
    technician_comments = models.TextField(
        blank=True,
        verbose_name="Technician Comments",
        help_text="Comments from the technician who performed the service"
    )
    supervisor_comments = models.TextField(
        blank=True,
        verbose_name="Supervisor Comments",
        help_text="Comments from the supervisor after inspection"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-service_date']
        verbose_name = "Service Record"
        verbose_name_plural = "Service Records"

    def __str__(self):
        return f"Service Record for {self.machine_part} on {self.service_date}"


@receiver(post_save, sender=ServiceRecord)
def update_schedule_completion(sender, instance, created, **kwargs):
    """
    Automatically mark associated service schedule as completed when
    service record status is set to 'completed'
    """
    if instance.status == 'completed' and instance.ticket_item:
        # Find the associated schedule through the ticket item
        if instance.ticket_item.referenced_schedule:
            schedule = instance.ticket_item.referenced_schedule
            if not schedule.is_completed:
                schedule.is_completed = True
                schedule.save(update_fields=['is_completed', 'updated_at'])
