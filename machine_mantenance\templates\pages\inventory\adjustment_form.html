{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ title }}</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        <strong>Inventory Adjustments</strong> are used to account for inventory changes that don't come from orders or service usage, such as damage, theft, requisitions, or corrections.
                    </div>
                    
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.part.label_tag }}
                                    {{ form.part }}
                                    {% if form.part.errors %}
                                        <div class="text-danger">{{ form.part.errors }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.adjustment_type.label_tag }}
                                    {{ form.adjustment_type }}
                                    {% if form.adjustment_type.errors %}
                                        <div class="text-danger">{{ form.adjustment_type.errors }}</div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        Choose "Addition" to increase inventory or "Deduction" to decrease it.
                                    </small>
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.quantity.label_tag }}
                                    {{ form.quantity }}
                                    {% if form.quantity.errors %}
                                        <div class="text-danger">{{ form.quantity.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.reason.label_tag }}
                                    {{ form.reason }}
                                    {% if form.reason.errors %}
                                        <div class="text-danger">{{ form.reason.errors }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.adjustment_date.label_tag }}
                                    {{ form.adjustment_date }}
                                    {% if form.adjustment_date.errors %}
                                        <div class="text-danger">{{ form.adjustment_date.errors }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.created_by.label_tag }}
                                    {{ form.created_by }}
                                    {% if form.created_by.errors %}
                                        <div class="text-danger">{{ form.created_by.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            {{ form.description.label_tag }}
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="text-danger">{{ form.description.errors }}</div>
                            {% endif %}
                            <small class="form-text text-muted">
                                Provide a detailed explanation for this inventory adjustment.
                            </small>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'inventory:adjustment_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save"></i> {{ submit_text }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-populate created_by field with current user if available
    const createdByField = document.getElementById('id_created_by');
    if (createdByField && !createdByField.value) {
        // You can set a default value here if needed
        // createdByField.value = 'Current User';
    }
    
    // Set default date to today
    const dateField = document.getElementById('id_adjustment_date');
    if (dateField && !dateField.value) {
        const today = new Date().toISOString().split('T')[0];
        dateField.value = today;
    }
});
</script>
{% endblock %}
