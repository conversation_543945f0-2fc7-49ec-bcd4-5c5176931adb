{% load static %}
<nav id="sidebar" class="sidebar">
    <a class='sidebar-brand' href='#'>
        <svg>
            <use xlink:href="#ion-ios-pulse-strong"></use>
        </svg>
        D. R. M. M. S.
    </a>
    <div class="sidebar-content">
        <div class="sidebar-user">
            <img src="{% static 'img/brands/machine_repair.png' %}" class="img-fluid rounded-circle mb-2" alt="Machine Maintenance Management System" />
            <div class="fw-bold">Drilling Rig Maintenance Management System</div>
        </div>

        <ul class="sidebar-nav">
            <li class="sidebar-item {% if request.resolver_match.url_name == 'dashboard' %}active{% endif %}">
                <a class='sidebar-link' href='{% url "dashboard:home" %}'>
                    <i class="align-middle me-2 fas fa-fw fa-home"></i> <span class="align-middle">Dashboard</span>
                </a>
            </li>
            <li class="sidebar-item {% if request.resolver_match.namespace == 'systems' %}active{% endif %}">
                <a class='sidebar-link' href='{% url "systems:list" %}'>
                    <i class="align-middle me-2 fas fa-fw fa-sitemap"></i> <span class="align-middle">Systems</span>
                </a>
            </li>
            <li class="sidebar-item {% if request.resolver_match.namespace == 'machines' %}active{% endif %}">
                <a class='sidebar-link' href='{% url "machines:list" %}'>
                    <i class="align-middle me-2 fas fa-fw fa-industry"></i> <span class="align-middle">Machines</span>
                </a>
            </li>
            <li class="sidebar-item {% if request.resolver_match.namespace == 'parts' %}active{% endif %}">
                <a class='sidebar-link' href='{% url "parts:list" %}'>
                    <i class="align-middle me-2 fas fa-fw fa-cubes"></i> <span class="align-middle">Parts</span>
                </a>
            </li>
            <li class="sidebar-item {% if request.resolver_match.namespace == 'inventory' %}active{% endif %}">
                <a data-bs-target="#inventory-menu" data-bs-toggle="collapse" class="sidebar-link {% if 'inventory' not in request.resolver_match.url_name %}collapsed{% endif %}">
                    <i class="align-middle me-2 fas fa-fw fa-warehouse"></i> <span class="align-middle">Inventory</span>
                </a>
                <ul id="inventory-menu" class="sidebar-dropdown list-unstyled collapse {% if request.resolver_match.namespace == 'inventory'  %}show{% endif %}" data-bs-parent="#sidebar">
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'inventory_dashboard' %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "inventory:inventory_dashboard" %}'>
                            <i class="align-middle me-2 fas fa-fw fa-tachometer-alt"></i> <span class="align-middle">Dashboard</span>
                        </a>
                    </li>
                    <li class="sidebar-item {% if 'order' in request.resolver_match.url_name %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "inventory:order_list" %}'>
                            <i class="align-middle me-2 fas fa-fw fa-shopping-cart"></i> <span class="align-middle">Orders</span>
                        </a>
                    </li>
                    <li class="sidebar-item {% if 'document' in request.resolver_match.url_name %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "inventory:document_list" %}'>
                            <i class="align-middle me-2 fas fa-fw fa-file-alt"></i> <span class="align-middle">Documents</span>
                        </a>
                    </li>
                    <li class="sidebar-item {% if 'adjustment' in request.resolver_match.url_name %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "inventory:adjustment_list" %}'>
                            <i class="align-middle me-2 fas fa-fw fa-adjust"></i> <span class="align-middle">Adjustments</span>
                        </a>
                    </li>
                </ul>
            </li>
            <li class="sidebar-item {% if request.resolver_match.namespace == 'service' %}active{% endif %}">
                <a data-bs-target="#service-menu" data-bs-toggle="collapse" class="sidebar-link {% if request.resolver_match.namespace != 'service' %}collapsed{% endif %}">
                    <i class="align-middle me-2 fas fa-fw fa-wrench"></i> <span class="align-middle">Service</span>
                </a>
                <ul id="service-menu" class="sidebar-dropdown list-unstyled collapse {% if request.resolver_match.namespace == 'service' %}show{% endif %}" data-bs-parent="#sidebar">
                    <li class="sidebar-item {% if request.resolver_match.namespace == 'service' and 'schedule' in request.resolver_match.url_name %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "service:schedule_list" %}'>
                            <i class="align-middle me-2 fas fa-fw fa-calendar-week"></i> <span class="align-middle">Schedule</span>
                        </a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.namespace == 'service' and 'ticket' in request.resolver_match.url_name %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "service:ticket_list" %}'>
                            <i class="align-middle me-2 fas fa-fw fa-receipt"></i> <span class="align-middle">Tickets</span>
                        </a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.namespace == 'service' and 'record' in request.resolver_match.url_name %}active{% endif %}">
                        <a class='sidebar-link' href='{% url "service:record_list" %}'>
                            <i class="align-middle me-2 fas fa-fw fa-table-list"></i> <span class="align-middle">Records</span>
                        </a>
                    </li>
                </ul>
            <li class="sidebar-item {% if request.resolver_match.namespace == 'technicians' %}active{% endif %}">
                <a class='sidebar-link' href='{% url "technicians:list" %}'>
                    <i class="align-middle me-2 fas fa-fw fa-user-tag"></i> <span class="align-middle">Technicians</span>
                </a>
            </li>
            <li class="sidebar-item {% if request.resolver_match.namespace == 'reports' %}active{% endif %}">
                <a data-bs-target="#documentation" data-bs-toggle="collapse" class="sidebar-link {% if request.resolver_match.namespace != 'docs' %}collapsed{% endif %}">
                    <i class="align-middle me-2 fas fa-fw fa-book"></i> <span class="align-middle">Reports</span>
                </a>
                <ul id="documentation" class="sidebar-dropdown list-unstyled collapse {% if request.resolver_match.namespace == 'docs' %}show{% endif %}" data-bs-parent="#sidebar">
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'getting_started' %}active{% endif %}">
                        <a class='sidebar-link' href='#'>Getting Started</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'plugins' %}active{% endif %}">
                        <a class='sidebar-link' href='#'>Plugins</a>
                    </li>
                    <li class="sidebar-item {% if request.resolver_match.url_name == 'changelog' %}active{% endif %}">
                        <a class='sidebar-link' href='#'>Changelog</a>
                    </li>
                </ul>
            </li>
        </ul>
    </div>
</nav>
