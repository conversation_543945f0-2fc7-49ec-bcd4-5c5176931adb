# Generated by Django 5.2.1 on 2025-09-11 11:52

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('service', '0002_alter_servicerecord_options_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='servicerecord',
            name='status',
            field=models.CharField(choices=[('incomplete', 'Incomplete'), ('inadequate', 'Inadequate'), ('completed', 'Completed'), ('needs_review', 'Needs Review')], default='incomplete', help_text='Status of the service work performed', max_length=20, verbose_name='Service Status'),
        ),
        migrations.AddField(
            model_name='serviceschedule',
            name='is_completed',
            field=models.BooleanField(default=False, help_text='Automatically set to True when associated service record is marked as complete', verbose_name='Is Completed'),
        ),
    ]
