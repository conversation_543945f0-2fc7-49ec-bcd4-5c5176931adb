{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ title }}</h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.part.label_tag }}
                                    {{ form.part }}
                                    {% if form.part.errors %}
                                        <div class="text-danger">{{ form.part.errors }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.quantity.label_tag }}
                                    {{ form.quantity }}
                                    {% if form.quantity.errors %}
                                        <div class="text-danger">{{ form.quantity.errors }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.order_date.label_tag }}
                                    {{ form.order_date }}
                                    {% if form.order_date.errors %}
                                        <div class="text-danger">{{ form.order_date.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                
                                <div class="mb-3">
                                    {{ form.supplier.label_tag }}
                                    {{ form.supplier }}
                                    {% if form.supplier.errors %}
                                        <div class="text-danger">{{ form.supplier.errors }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.order_reference_document.label_tag }}
                                    {{ form.order_reference_document }}
                                    {% if form.order_reference_document.errors %}
                                        <div class="text-danger">{{ form.order_reference_document.errors }}</div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        Optional: Link to an existing order document, or 
                                        <a href="{% url 'inventory:document_create' %}" target="_blank">upload a new one</a>
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'inventory:order_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> {{ submit_text }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
