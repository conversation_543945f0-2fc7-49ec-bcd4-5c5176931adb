{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ title }}</h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.document_name.label_tag }}
                                    {{ form.document_name }}
                                    {% if form.document_name.errors %}
                                        <div class="text-danger">{{ form.document_name.errors }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="mb-3">
                                    {{ form.document_type.label_tag }}
                                    {{ form.document_type }}
                                    {% if form.document_type.errors %}
                                        <div class="text-danger">{{ form.document_type.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.document_file.label_tag }}
                                    {{ form.document_file }}
                                    {% if form.document_file.errors %}
                                        <div class="text-danger">{{ form.document_file.errors }}</div>
                                    {% endif %}
                                    <small class="form-text text-muted">
                                        Upload purchase orders, invoices, receipts, or other order-related documents.
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            {{ form.notes.label_tag }}
                            {{ form.notes }}
                            {% if form.notes.errors %}
                                <div class="text-danger">{{ form.notes.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'inventory:document_list' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload"></i> {{ submit_text }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
