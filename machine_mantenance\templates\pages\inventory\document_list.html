{% extends 'base/base.html' %}
{% load static %}

{% block title %}Order Documents{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Order Documents</h5>
                    <a href="{% url 'inventory:document_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Upload Document
                    </a>
                </div>
                <div class="card-body">
                    {% if page_obj %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Document Name</th>
                                        <th>Type</th>
                                        <th>Upload Date</th>
                                        <th>Associated Orders</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for document in page_obj %}
                                    <tr>
                                        <td>
                                            <a href="{% url 'inventory:document_detail' document.pk %}" class="text-decoration-none">
                                                <strong>{{ document.document_name }}</strong>
                                            </a>
                                        </td>
                                        <td>
                                            <span class="badge bg-primary">{{ document.get_document_type_display }}</span>
                                        </td>
                                        <td>{{ document.upload_date|date:"M d, Y g:i A" }}</td>
                                        <td>
                                            <span class="badge bg-info">{{ document.part_orders.count }} order{{ document.part_orders.count|pluralize }}</span>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{% url 'inventory:document_detail' document.pk %}" 
                                                   class="btn btn-outline-primary" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                {% if document.document_file %}
                                                <a href="{{ document.document_file.url }}" target="_blank"
                                                   class="btn btn-outline-success" title="Download File">
                                                    <i class="fas fa-download"></i>
                                                </a>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if page_obj.has_other_pages %}
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1">First</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                                    </li>
                                {% endif %}

                                <li class="page-item active">
                                    <span class="page-link">
                                        Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                                    </span>
                                </li>

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}">Next</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">Last</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}

                        <!-- Results Summary -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <small class="text-muted">
                                Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ total_count }} documents
                            </small>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Documents Found</h5>
                            <p class="text-muted">No order documents have been uploaded yet.</p>
                            <a href="{% url 'inventory:document_create' %}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Upload First Document
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .table td {
        vertical-align: middle;
    }
    
    .badge {
        font-size: 0.875rem;
    }
    
    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
    }
</style>
{% endblock %}
