"""
URL patterns for machine management.
"""
from django.urls import path
from . import views

app_name = 'machines'

urlpatterns = [
    # Machine URLs
    path('', views.machine_list, name='list'),
    path('<int:pk>/', views.machine_detail, name='detail'),
    path('create/', views.machine_create, name='create'),
    path('<int:pk>/edit/', views.machine_edit, name='edit'),
    path('<int:pk>/delete/', views.machine_delete, name='delete'),

    # Machine Part URLs
    path('add-part/', views.machine_part_create, name='add_part'),
    path('<int:machine_pk>/add-part/', views.machine_part_create, name='add_part_to_machine'),
    path('machine-part/<int:pk>/edit/', views.machine_part_edit, name='edit_machine_part'),

    # Inventory Management URLs
    path('inventory/', views.inventory_dashboard, name='inventory_dashboard'),

    # Order Management URLs
    path('inventory/orders/', views.order_list, name='order_list'),
    path('inventory/orders/create/', views.order_create, name='order_create'),
    path('inventory/orders/<int:pk>/edit/', views.order_edit, name='order_edit'),

    # Document Management URLs
    path('inventory/documents/', views.document_list, name='document_list'),
    path('inventory/documents/create/', views.document_create, name='document_create'),

    # Inventory Adjustment URLs
    path('inventory/adjustments/', views.adjustment_list, name='adjustment_list'),
    path('inventory/adjustments/create/', views.adjustment_create, name='adjustment_create'),
    path('inventory/adjustments/<int:pk>/', views.adjustment_detail, name='adjustment_detail'),
]
