{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .formset-row {
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        padding: 1rem;
        margin-bottom: 1rem;
        background-color: #f8f9fa;
    }
    
    .formset-row.to-delete {
        opacity: 0.5;
        background-color: #f8d7da;
    }
    
    .delete-row-btn {
        background: none;
        border: none;
        color: #dc3545;
        font-size: 1.2rem;
        cursor: pointer;
        float: right;
    }
    
    .delete-row-btn:hover {
        color: #c82333;
    }
    
    .add-row-btn {
        background-color: #28a745;
        border: none;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 0.375rem;
        cursor: pointer;
        margin-top: 1rem;
    }
    
    .add-row-btn:hover {
        background-color: #218838;
    }
    
    .part-info {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.25rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ title }}</h5>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data" id="multi-part-order-form">
                        {% csrf_token %}
                        
                        <!-- Order Document Information -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary mb-3">Order Document Information</h6>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ order_form.document_name.label_tag }}
                                    {{ order_form.document_name }}
                                    {% if order_form.document_name.errors %}
                                        <div class="text-danger">{{ order_form.document_name.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="mb-3">
                                    {{ order_form.document_type.label_tag }}
                                    {{ order_form.document_type }}
                                    {% if order_form.document_type.errors %}
                                        <div class="text-danger">{{ order_form.document_type.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="mb-3">
                                    {{ order_form.order_date.label_tag }}
                                    {{ order_form.order_date }}
                                    {% if order_form.order_date.errors %}
                                        <div class="text-danger">{{ order_form.order_date.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ order_form.document_file.label_tag }}
                                    {{ order_form.document_file }}
                                    {% if order_form.document_file.errors %}
                                        <div class="text-danger">{{ order_form.document_file.errors }}</div>
                                    {% endif %}
                                </div>
                                <div class="mb-3">
                                    {{ order_form.supplier.label_tag }}
                                    {{ order_form.supplier }}
                                    {% if order_form.supplier.errors %}
                                        <div class="text-danger">{{ order_form.supplier.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="mb-3">
                                    {{ order_form.notes.label_tag }}
                                    {{ order_form.notes }}
                                    {% if order_form.notes.errors %}
                                        <div class="text-danger">{{ order_form.notes.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- Parts Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="text-primary mb-3">Order Items</h6>
                                <div id="formset-container">
                                    {{ formset.management_form }}
                                    {% if formset.non_form_errors %}
                                        <div class="alert alert-danger">
                                            {{ formset.non_form_errors }}
                                        </div>
                                    {% endif %}
                                    
                                    <div id="formset-forms">
                                        {% for form in formset %}
                                            <div class="formset-row" data-form-index="{{ forloop.counter0 }}">
                                                <button type="button" class="delete-row-btn" title="Remove this item">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                                
                                                <div class="row">
                                                    <div class="col-md-5">
                                                        <div class="mb-3">
                                                            {{ form.part.label_tag }}
                                                            {{ form.part }}
                                                            {% if form.part.errors %}
                                                                <div class="text-danger">{{ form.part.errors }}</div>
                                                            {% endif %}
                                                        </div>
                                                    </div>
                                                    <div class="col-md-3">
                                                        <div class="mb-3">
                                                            {{ form.quantity.label_tag }}
                                                            {{ form.quantity }}
                                                            {% if form.quantity.errors %}
                                                                <div class="text-danger">{{ form.quantity.errors }}</div>
                                                            {% endif %}
                                                        </div>
                                                    </div>
                                                </div>
                                                
                                                <!-- Hidden delete field -->
                                                {{ form.DELETE }}
                                            </div>
                                        {% endfor %}
                                    </div>
                                    
                                    <button type="button" class="add-row-btn" id="add-form-btn">
                                        <i class="fas fa-plus"></i> Add Another Part
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="row">
                            <div class="col-12">
                                <div class="d-flex justify-content-between">
                                    <a href="{% url 'inventory:order_list' %}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left"></i> Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save"></i> {{ submit_text }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const formsetContainer = document.getElementById('formset-container');
    const addButton = document.getElementById('add-form-btn');
    const totalFormsInput = document.getElementById('id_form-TOTAL_FORMS');
    
    let formIndex = parseInt(totalFormsInput.value);
    
    // Add new form functionality
    addButton.addEventListener('click', function() {
        const emptyFormHtml = document.getElementById('empty-form').innerHTML.replace(/__prefix__/g, formIndex);
        const newFormDiv = document.createElement('div');
        newFormDiv.className = 'formset-row';
        newFormDiv.setAttribute('data-form-index', formIndex);
        newFormDiv.innerHTML = emptyFormHtml;
        
        document.getElementById('formset-forms').appendChild(newFormDiv);
        
        formIndex++;
        totalFormsInput.value = formIndex;
        
        // Add delete functionality to new form
        addDeleteFunctionality(newFormDiv);
    });
    
    // Add delete functionality to existing forms
    document.querySelectorAll('.formset-row').forEach(addDeleteFunctionality);
    
    function addDeleteFunctionality(formRow) {
        const deleteBtn = formRow.querySelector('.delete-row-btn');
        deleteBtn.addEventListener('click', function() {
            const deleteInput = formRow.querySelector('input[name$="-DELETE"]');
            if (deleteInput) {
                deleteInput.checked = true;
                formRow.classList.add('to-delete');
                formRow.style.display = 'none';
            } else {
                formRow.remove();
            }
        });
    }
});
</script>

<!-- Empty form template for JavaScript -->
<div id="empty-form" style="display: none;">
    <button type="button" class="delete-row-btn" title="Remove this item">
        <i class="fas fa-times"></i>
    </button>
    <div class="row">
        <div class="col-md-5">
            <div class="mb-3">
                <label for="id_form-__prefix__-part">Part:</label>
                <select name="form-__prefix__-part" class="form-select part-select" id="id_form-__prefix__-part">
                    <option value="">Select a part...</option>
                    {% for part in formset.empty_form.part.field.queryset %}
                        <option value="{{ part.pk }}">{{ part }}</option>
                    {% endfor %}
                </select>
            </div>
        </div>
        <div class="col-md-3">
            <div class="mb-3">
                <label for="id_form-__prefix__-quantity">Quantity:</label>
                <input type="number" name="form-__prefix__-quantity" class="form-control quantity-input" placeholder="Qty" min="1" id="id_form-__prefix__-quantity">
            </div>
        </div>
        <div class="col-md-4">    </div>
    <input type="checkbox" name="form-__prefix__-DELETE" id="id_form-__prefix__-DELETE" style="display: none;">
</div>
{% endblock %}
