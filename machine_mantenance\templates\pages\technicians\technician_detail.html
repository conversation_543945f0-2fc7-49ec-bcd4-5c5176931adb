{% extends 'base/base.html' %}
{% load static %}

{% block title %}{{ technician.name }} - {{ block.super }}{% endblock %}

{% block page_header %}
<div class="header">
    <h1 class="header-title">
        {{ technician.name }}
    </h1>
    <p class="header-subtitle">Technician profile and service history.</p>
</div>
{% endblock %}

{% block content %}
<div class="row">
    <!-- Technician Information -->
    <div class="col-12 col-lg-8">
        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="card-title mb-0">Technician Information</h5>
                    </div>
                    <div class="col-auto">
                        <a href="{% url 'technicians:edit' technician.pk %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-edit me-1"></i> Edit
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">Name:</dt>
                            <dd class="col-sm-8">{{ technician.name }}</dd>
                            
                            <dt class="col-sm-4">Email:</dt>
                            <dd class="col-sm-8">
                                {% if technician.email %}
                                    <a href="mailto:{{ technician.email }}">{{ technician.email }}</a>
                                {% else %}
                                    <span class="text-muted">Not provided</span>
                                {% endif %}
                            </dd>
                            
                            <dt class="col-sm-4">Phone:</dt>
                            <dd class="col-sm-8">
                                {% if technician.phone %}
                                    <a href="tel:{{ technician.phone }}">{{ technician.phone }}</a>
                                {% else %}
                                    <span class="text-muted">Not provided</span>
                                {% endif %}
                            </dd>
                        </dl>
                    </div>
                    <div class="col-md-6">
                        <dl class="row">
                            <dt class="col-sm-4">Created:</dt>
                            <dd class="col-sm-8">{{ technician.created_at|date:"M d, Y" }}</dd>
                            
                            <dt class="col-sm-4">Last Updated:</dt>
                            <dd class="col-sm-8">{{ technician.updated_at|date:"M d, Y" }}</dd>
                        </dl>
                    </div>
                </div>
                
                {% if technician.specialization %}
                <div class="mt-3">
                    <h6>Specialization & Skills:</h6>
                    <p class="text-muted">{{ technician.specialization }}</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Quick Stats -->
    <div class="col-12 col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Performance Stats</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="mb-3">
                            <h4 class="text-primary">{{ recent_records.count }}</h4>
                            <small class="text-muted">Total Services</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="mb-3">
                            <h4 class="text-warning">{{ upcoming_schedules.count }}</h4>
                            <small class="text-muted">Upcoming</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Service Records -->
<div class="row mt-4">
    <div class="col-12 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Recent Service Records</h5>
            </div>
            <div class="card-body">
                {% if recent_records %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Machine</th>
                                <th>Part</th>
                                <th>Parts Used</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in recent_records %}
                            <tr>
                                <td>{{ record.service_date|date:"M d" }}</td>
                                <td>
                                    <a href="{% url 'machines:detail' record.machine_part.machine.pk %}">
                                        {{ record.machine_part.machine.name|truncatechars:20 }}
                                    </a>
                                </td>
                                <td>{{ record.machine_part.part|truncatechars:20 }}</td>
                                <td>
                                    {% if record.number_of_parts_replaced > 0 %}
                                        <span class="badge bg-primary">{{ record.number_of_parts_replaced }}</span>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="text-center mt-3">
                    <a href="{% url 'service:record_list' %}?technician_name={{ technician.name }}" class="btn btn-sm btn-outline-primary">
                        View All Records
                    </a>
                </div>
                {% else %}
                <div class="text-center text-muted py-3">
                    <i class="fas fa-wrench fa-2x mb-2"></i>
                    <p>No service records yet.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Upcoming Schedules -->
    <div class="col-12 col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Upcoming Schedules</h5>
            </div>
            <div class="card-body">
                {% if upcoming_schedules %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Machine</th>
                                <th>Part</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for schedule in upcoming_schedules %}
                            <tr>
                                <td>{{ schedule.scheduled_date|date:"M d" }}</td>
                                <td>{{ schedule.machine_part.machine.name|truncatechars:20 }}</td>
                                <td>{{ schedule.machine_part.part|truncatechars:20 }}</td>
                                <td>
                                    {% if schedule.status == 'overdue' %}
                                        <span class="badge bg-danger">Overdue</span>
                                    {% elif schedule.status == 'due_today' %}
                                        <span class="badge bg-warning">Due Today</span>
                                    {% else %}
                                        <span class="badge bg-success">Upcoming</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="text-center mt-3">
                    <a href="{% url 'service:schedule_list' %}" class="btn btn-sm btn-outline-primary">
                        View All Schedules
                    </a>
                </div>
                {% else %}
                <div class="text-center text-muted py-3">
                    <i class="fas fa-calendar fa-2x mb-2"></i>
                    <p>No upcoming schedules.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
