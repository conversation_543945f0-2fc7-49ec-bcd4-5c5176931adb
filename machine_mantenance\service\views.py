from django.shortcuts import render, get_object_or_404, redirect
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone
from django.http import JsonResponse, HttpResponse
from django.template.loader import get_template
from .models import Technician, ServiceSchedule, ServiceTicket, ServiceTicketItem, ServiceRecord
from .forms import (
    TechnicianForm, ServiceScheduleForm, ServiceTicketForm,
    ServiceTicketItemForm, ServiceRecordForm,
    TechnicianSearchForm, ServiceRecordSearchForm
)


# Technician Views
def technician_list(request):
    """List all technicians with search functionality"""
    search_form = TechnicianSearchForm(request.GET)
    technicians = Technician.objects.all()

    if search_form.is_valid():
        name = search_form.cleaned_data.get('name')
        specialization = search_form.cleaned_data.get('specialization')

        if name:
            technicians = technicians.filter(name__icontains=name)
        if specialization:
            technicians = technicians.filter(specialization__icontains=specialization)

    paginator = Paginator(technicians, 25)  # Show 25 technicians per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_form': search_form,
        'total_count': technicians.count(),
    }
    return render(request, 'pages/technicians/technician_list.html', context)


def technician_detail(request, pk):
    """Display technician details with job history"""
    technician = get_object_or_404(Technician, pk=pk)
    recent_records = technician.service_records.all()[:10]  # Recent service records
    upcoming_schedules = technician.service_schedules.filter(
        scheduled_date__gte=timezone.now().date()
    )[:10]

    context = {
        'technician': technician,
        'recent_records': recent_records,
        'upcoming_schedules': upcoming_schedules,
    }
    return render(request, 'pages/technicians/technician_detail.html', context)


def technician_create(request):
    """Create a new technician"""
    if request.method == 'POST':
        form = TechnicianForm(request.POST)
        if form.is_valid():
            technician = form.save()
            messages.success(request, f'Technician "{technician.name}" created successfully!')
            return redirect('technicians:detail', pk=technician.pk)
    else:
        form = TechnicianForm()

    context = {
        'form': form,
        'title': 'Add New Technician',
        'submit_text': 'Create Technician',
    }
    return render(request, 'pages/technicians/technician_form.html', context)


def technician_edit(request, pk):
    """Edit an existing technician"""
    technician = get_object_or_404(Technician, pk=pk)

    if request.method == 'POST':
        form = TechnicianForm(request.POST, instance=technician)
        if form.is_valid():
            technician = form.save()
            messages.success(request, f'Technician "{technician.name}" updated successfully!')
            return redirect('technicians:detail', pk=technician.pk)
    else:
        form = TechnicianForm(instance=technician)

    context = {
        'form': form,
        'technician': technician,
        'title': f'Edit Technician: {technician.name}',
        'submit_text': 'Update Technician',
    }
    return render(request, 'pages/technicians/technician_form.html', context)


# Service Schedule Views
def service_schedule_list(request):
    """List all service schedules with status indicators"""
    schedules = ServiceSchedule.objects.all().order_by('scheduled_date')

    # Separate by status
    overdue = []
    due_today = []
    upcoming = []

    for schedule in schedules:
        if schedule.status == 'overdue':
            overdue.append(schedule)
        elif schedule.status == 'due_today':
            due_today.append(schedule)
        else:
            upcoming.append(schedule)

    context = {
        'overdue': overdue,
        'due_today': due_today,
        'upcoming': upcoming[:20],  # Limit upcoming to 20 items
    }
    return render(request, 'pages/service/schedule_list.html', context)


def service_schedule_create(request):
    """Create a new service schedule"""
    if request.method == 'POST':
        form = ServiceScheduleForm(request.POST)
        if form.is_valid():
            schedule = form.save()
            messages.success(request, f'Service scheduled for {schedule.machine_part} on {schedule.scheduled_date}!')
            return redirect('service:schedule_list')
    else:
        form = ServiceScheduleForm()

    context = {
        'form': form,
        'title': 'Schedule Service',
        'submit_text': 'Create Schedule',
    }
    return render(request, 'pages/service/schedule_form.html', context)

def service_schedule_edit(request, pk):
    """Edit an existing service schedule"""
    schedule = get_object_or_404(ServiceSchedule, pk=pk)

    if request.method == 'POST':
        form = ServiceScheduleForm(request.POST, instance=schedule)
        if form.is_valid():
            schedule = form.save()
            messages.success(request, f'Service schedule updated successfully!')
            return redirect('service:schedule_list')
    else:
        form = ServiceScheduleForm(instance=schedule)

    context = {
        'form': form,
        'schedule': schedule,
        'title': f'Edit Service Schedule',
        'submit_text': 'Update Schedule',
    }
    return render(request, 'pages/service/schedule_form.html', context)

# Service Ticket Views
def service_ticket_list(request):
    """List all service tickets"""
    tickets = ServiceTicket.objects.all()

    paginator = Paginator(tickets, 25)  # Show 25 tickets per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'total_count': tickets.count(),
    }
    return render(request, 'pages/service/ticket_list.html', context)


def service_ticket_detail(request, pk):
    """Display service ticket details with items and available schedules"""
    ticket = get_object_or_404(ServiceTicket, pk=pk)
    ticket_items = ticket.ticket_items.all()

    # Get machine parts that are already on this ticket
    ticketed_machine_parts = ticket_items.values_list('machine_part', flat=True)

    # Get available schedules (not already on this ticket)
    available_schedules = ServiceSchedule.objects.exclude(
        machine_part__in=ticketed_machine_parts
    ).select_related('machine_part__machine', 'machine_part__part', 'assigned_technician')

    # Sort schedules based on ticket technician priority
    if ticket.assigned_technician:
        # Same technician first, then unassigned, then others
        same_technician = available_schedules.filter(assigned_technician=ticket.assigned_technician).order_by('scheduled_date')
        unassigned = available_schedules.filter(assigned_technician__isnull=True).order_by('scheduled_date')
        others = available_schedules.exclude(assigned_technician=ticket.assigned_technician).exclude(assigned_technician__isnull=True).order_by('scheduled_date')

        # Combine the querysets
        from itertools import chain
        available_schedules = list(chain(same_technician, unassigned, others))
    else:
        # If ticket has no technician, show unassigned first, then others
        unassigned = available_schedules.filter(assigned_technician__isnull=True).order_by('scheduled_date')
        others = available_schedules.exclude(assigned_technician__isnull=True).order_by('scheduled_date')
        available_schedules = list(chain(unassigned, others))

    # Create form for adding manual items
    add_item_form = ServiceTicketItemForm()

    context = {
        'ticket': ticket,
        'ticket_items': ticket_items,
        'available_schedules': available_schedules,
        'add_item_form': add_item_form,
        'today': timezone.now().date(),
    }
    return render(request, 'pages/service/ticket_detail.html', context)


def service_ticket_create(request):
    """Create a new service ticket"""
    if request.method == 'POST':
        form = ServiceTicketForm(request.POST)
        if form.is_valid():
            ticket = form.save()
            messages.success(request, f'Service ticket created for {ticket.service_date}!')
            return redirect('service:ticket_detail', pk=ticket.pk)
    else:
        form = ServiceTicketForm()

    context = {
        'form': form,
        'title': 'Create Service Ticket',
        'submit_text': 'Create Ticket',
    }
    return render(request, 'pages/service/ticket_form.html', context)


def add_schedule_to_ticket(request, ticket_pk, schedule_pk):
    """Add a scheduled item to a service ticket"""
    ticket = get_object_or_404(ServiceTicket, pk=ticket_pk)
    schedule = get_object_or_404(ServiceSchedule, pk=schedule_pk)

    # Check if this machine part is already on the ticket
    existing_item = ServiceTicketItem.objects.filter(
        service_ticket=ticket,
        machine_part=schedule.machine_part
    ).first()

    if existing_item:
        messages.warning(request, f'This machine part is already on the ticket.')
    else:
        # Create the ticket item
        ServiceTicketItem.objects.create(
            service_ticket=ticket,
            machine_part=schedule.machine_part,
            referenced_schedule=schedule
        )
        messages.success(request, f'Added {schedule.machine_part} to ticket from schedule.')

    return redirect('service:ticket_detail', pk=ticket.pk)


def add_manual_item_to_ticket(request, ticket_pk):
    """Add a manual item to a service ticket"""
    ticket = get_object_or_404(ServiceTicket, pk=ticket_pk)

    if request.method == 'POST':
        # Get the machine_part ID directly from POST data
        machine_part_id = request.POST.get('machine_part')

        if machine_part_id:
            try:
                from machines.models import MachinePart
                machine_part = MachinePart.objects.get(id=machine_part_id)

                # Check if this machine part is already on the ticket
                existing_item = ServiceTicketItem.objects.filter(
                    service_ticket=ticket,
                    machine_part=machine_part
                ).first()

                if existing_item:
                    messages.warning(request, f'This machine part is already on the ticket.')
                else:
                    # Create the ticket item (without referenced_schedule for manual items)
                    ServiceTicketItem.objects.create(
                        service_ticket=ticket,
                        machine_part=machine_part,
                        referenced_schedule=None  # Manual items don't reference schedules
                    )
                    messages.success(request, f'Added {machine_part} to ticket manually.')
            except MachinePart.DoesNotExist:
                messages.error(request, 'Selected machine part not found.')
        else:
            messages.error(request, 'Please select a machine part.')

    return redirect('service:ticket_detail', pk=ticket.pk)


def get_machine_parts_ajax(request):
    """AJAX endpoint to get parts for a selected machine"""
    machine_id = request.GET.get('machine_id')
    if machine_id:
        from machines.models import MachinePart
        machine_parts = MachinePart.objects.filter(machine_id=machine_id).select_related('part')
        data = [
            {
                'id': mp.id,
                'text': f"{mp.part.part_no} - {mp.part.description}"
            }
            for mp in machine_parts
        ]
        return JsonResponse({'results': data})
    return JsonResponse({'results': []})


def edit_ticket_item(request, ticket_pk, item_pk):
    """Edit a service ticket item"""
    ticket = get_object_or_404(ServiceTicket, pk=ticket_pk)
    item = get_object_or_404(ServiceTicketItem, pk=item_pk, service_ticket=ticket)

    if request.method == 'POST':
        machine_part_id = request.POST.get('machine_part')

        if machine_part_id:
            try:
                from machines.models import MachinePart
                new_machine_part = MachinePart.objects.get(id=machine_part_id)

                # Check if this machine part is already on the ticket (excluding current item)
                existing_item = ServiceTicketItem.objects.filter(
                    service_ticket=ticket,
                    machine_part=new_machine_part
                ).exclude(pk=item.pk).first()

                if existing_item:
                    messages.warning(request, f'This machine part is already on the ticket.')
                else:
                    # Store old referenced schedule for potential restoration
                    old_referenced_schedule = item.referenced_schedule

                    # Update the item
                    item.machine_part = new_machine_part
                    # Clear referenced schedule since this is now a manual edit
                    item.referenced_schedule = None
                    item.save()

                    messages.success(request, f'Updated ticket item to {new_machine_part}.')

            except MachinePart.DoesNotExist:
                messages.error(request, 'Selected machine part not found.')
        else:
            messages.error(request, 'Please select a machine part.')

    return redirect('service:ticket_detail', pk=ticket.pk)


def remove_ticket_item(request, ticket_pk, item_pk):
    """Remove a service ticket item"""
    ticket = get_object_or_404(ServiceTicket, pk=ticket_pk)
    item = get_object_or_404(ServiceTicketItem, pk=item_pk, service_ticket=ticket)

    if request.method == 'POST':
        # Store the referenced schedule before deletion (if any)
        referenced_schedule = item.referenced_schedule
        machine_part_name = str(item.machine_part)

        # Delete the item
        item.delete()

        messages.success(request, f'Removed {machine_part_name} from ticket.')

        # Note: The referenced schedule will automatically become available again
        # in the available schedules list since it's no longer referenced by any ticket item

    return redirect('service:ticket_detail', pk=ticket.pk)


def service_ticket_pdf(request, pk):
    """Generate a printable PDF worksheet for a service ticket"""
    ticket = get_object_or_404(ServiceTicket, pk=pk)
    ticket_items = ticket.ticket_items.all().select_related(
        'machine_part__machine',
        'machine_part__part',
        'referenced_schedule'
    )

    # Create the HttpResponse object with PDF headers
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename="service_ticket_{ticket.pk}.pdf"'

    # Generate PDF using ReportLab
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak, Image
    from reportlab.lib import colors
    from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
    from django.conf import settings
    from pathlib import Path

    # Create the PDF document with better margins for printing
    doc = SimpleDocTemplate(response, pagesize=A4,
                          rightMargin=0.75*inch, leftMargin=0.75*inch,
                          topMargin=0.75*inch, bottomMargin=1*inch)

    # Container for the 'Flowable' objects
    elements = []

    # Define styles
    styles = getSampleStyleSheet()

    # Logo and system title style
    logo_style = ParagraphStyle(
        'LogoStyle',
        parent=styles['Heading1'],
        fontSize=20,
        spaceAfter=10,
        alignment=TA_CENTER,
        textColor=colors.darkblue,
        fontName='Helvetica-Bold'
    )

    system_name_style = ParagraphStyle(
        'SystemNameStyle',
        parent=styles['Normal'],
        fontSize=16,
        spaceAfter=25,
        alignment=TA_CENTER,
        textColor=colors.black,
        fontName='Helvetica-Bold'
    )

    ticket_title_style = ParagraphStyle(
        'TicketTitleStyle',
        parent=styles['Heading2'],
        fontSize=16,
        spaceAfter=20,
        alignment=TA_CENTER,
        textColor=colors.black,
        fontName='Helvetica-Bold'
    )

    info_label_style = ParagraphStyle(
        'InfoLabelStyle',
        parent=styles['Normal'],
        fontSize=11,
        fontName='Helvetica-Bold'
    )

    item_header_style = ParagraphStyle(
        'ItemHeaderStyle',
        parent=styles['Heading3'],
        fontSize=12,
        spaceAfter=8,
        textColor=colors.black,
        fontName='Helvetica-Bold'
    )

    # Add logo image and system name
    logo_path = settings.BASE_DIR / 'static' / 'img' / 'brands' / 'machine_repair.png'  # Static files path in Django project (e.g., /static/img/brands/machine_repair.png)

    if logo_path.exists():
        logo_img = Image(logo_path, width=0.8*inch, height=0.8*inch)
        logo_img.hAlign = 'CENTER'
        # elements.append(logo_img)
        # elements.append(Spacer(1, 10))
    else:
        # Fallback to emoji if image not found
        logo_img = Paragraph("🔧", logo_style)
    title_info =  [[Image(logo_path, width=0.8*inch, height=0.8*inch), Paragraph("Drilling Rig Maintenance Management System", system_name_style)]]
    title_table = Table(title_info, colWidths=[1*inch, 5*inch])
    title_table.setStyle(TableStyle([
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
    ]))
    elements.append(title_table)

    # Add ticket title
    elements.append(Paragraph("MACHINE SERVICE TICKET", ticket_title_style))
    elements.append(Spacer(1, 20))

    # Add ticket information in a cleaner format
    technician_name = ticket.assigned_technician.name if ticket.assigned_technician else 'Unassigned'
    service_date = ticket.service_date.strftime("%B %d, %Y")

    # Create header information table
    header_info = [
        ['Technician:', technician_name, 'Service Date:', service_date],
        ['Ticket #:', f"#{ticket.pk}", 'Total Items:', str(ticket_items.count())]
    ]

    header_table = Table(header_info, colWidths=[1.2*inch, 2.3*inch, 1.2*inch, 2.3*inch])
    header_table.setStyle(TableStyle([
        ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
        ('FONTNAME', (2, 0), (2, -1), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, -1), 11),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
        ('TOPPADDING', (0, 0), (-1, -1), 8),
        ('BOX', (0, 0), (-1, -1), 1, colors.black),
        ('INNERGRID', (0, 0), (-1, -1), 0.5, colors.grey),
        ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
        ('BACKGROUND', (2, 0), (2, -1), colors.lightgrey),
    ]))

    elements.append(header_table)
    elements.append(Spacer(1, 30))

    # Service items section
    for i, item in enumerate(ticket_items, 1):
        # Item header with machine, part, and location
        machine_name = item.machine_part.machine.name
        part_info = f"{item.machine_part.part.part_no} - {item.machine_part.part.description}"
        location = item.machine_part.machine.location or 'Not specified'

        item_header = f"{i}. Machine: {machine_name} | Part: {part_info} | Location: {location}"
        elements.append(Paragraph(item_header, item_header_style))
        elements.append(Spacer(1, 8))

        # Parts used box - single row with label and blank space
        parts_used_data = [
            ['Parts used:', '_____ units of ' + item.machine_part.part.part_no]
        ]
        parts_table = Table(parts_used_data, colWidths=[1.2*inch, 5.3*inch])
        parts_table.setStyle(TableStyle([
            ('BOX', (0, 0), (-1, -1), 1.5, colors.black),
            ('FONTNAME', (0, 0), (0, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 11),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('LEFTPADDING', (0, 0), (-1, -1), 10),
            ('RIGHTPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 10),
            ('BACKGROUND', (0, 0), (0, 0), colors.lightgrey),
        ]))
        elements.append(parts_table)
        elements.append(Spacer(1, 12))

        # Two-column layout for service instructions and technician comments
        instructions_text = item.machine_part.service_instructions or "No specific instructions provided."

        # Format instructions text to fit better in the cell
        if len(instructions_text) > 300:
            instructions_text = instructions_text[:300] + "..."

        two_column_data = [
            ['Service Instructions', 'Technician Comments'],
            [instructions_text, '']  # Empty cell for technician to write comments
        ]

        # Make the table larger for better writing space
        instructions_table = Table(two_column_data, colWidths=[3.25*inch, 3.25*inch], rowHeights=[0.4*inch, 2.5*inch])
        instructions_table.setStyle(TableStyle([
            ('BOX', (0, 0), (-1, -1), 1.5, colors.black),
            ('INNERGRID', (0, 0), (-1, -1), 1, colors.black),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 11),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('ALIGN', (0, 1), (-1, -1), 'LEFT'),
            ('VALIGN', (0, 0), (-1, 0), 'MIDDLE'),
            ('VALIGN', (0, 1), (-1, -1), 'TOP'),
            ('LEFTPADDING', (0, 0), (-1, -1), 10),
            ('RIGHTPADDING', (0, 0), (-1, -1), 10),
            ('TOPPADDING', (0, 0), (-1, -1), 8),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightgrey),
        ]))

        elements.append(instructions_table)
        elements.append(Spacer(1, 30))

    # Add signature section at the bottom
    elements.append(Spacer(1, 20))

    # Create signature line
    signature_data = [
        ['Technician Signature:', '_' * 50, 'Date:', '_' * 25]
    ]

    signature_table = Table(signature_data, colWidths=[1.8*inch, 3.2*inch, 0.8*inch, 1.7*inch])
    signature_table.setStyle(TableStyle([
        ('FONTNAME', (0, 0), (0, 0), 'Helvetica-Bold'),
        ('FONTNAME', (2, 0), (2, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, -1), 11),
        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ('VALIGN', (0, 0), (-1, -1), 'BOTTOM'),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 20),
        ('TOPPADDING', (0, 0), (-1, -1), 10),
    ]))

    elements.append(signature_table)

    # Build PDF
    doc.build(elements)

    return response


# Service Record Views
def service_record_list(request):
    """List all service records with search functionality"""
    search_form = ServiceRecordSearchForm(request.GET)
    records = ServiceRecord.objects.all()

    if search_form.is_valid():
        machine_name = search_form.cleaned_data.get('machine_name')
        part_name = search_form.cleaned_data.get('part_name')
        technician_name = search_form.cleaned_data.get('technician_name')
        date_from = search_form.cleaned_data.get('date_from')
        date_to = search_form.cleaned_data.get('date_to')

        if machine_name:
            records = records.filter(machine_part__machine__name__icontains=machine_name)
        if part_name:
            records = records.filter(machine_part__part__name__icontains=part_name)
        if technician_name:
            records = records.filter(technician__name__icontains=technician_name)
        if date_from:
            records = records.filter(service_date__gte=date_from)
        if date_to:
            records = records.filter(service_date__lte=date_to)

    paginator = Paginator(records, 25)  # Show 25 records per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_form': search_form,
        'total_count': records.count(),
    }
    return render(request, 'pages/service/record_list.html', context)


def service_record_detail(request, pk):
    """Display service record details"""
    record = get_object_or_404(ServiceRecord, pk=pk)

    context = {
        'record': record,
    }
    return render(request, 'pages/service/record_detail.html', context)


def service_record_create(request):
    """Create a new service record"""
    if request.method == 'POST':
        form = ServiceRecordForm(request.POST)
        if form.is_valid():
            record = form.save()
            messages.success(request, f'Service record created for {record.machine_part}!')

            # Check if user clicked "Save & Schedule Next"
            if 'schedule_next' in request.POST:
                # Create next service schedule based on service frequency
                from datetime import timedelta
                next_date = record.service_date + timedelta(days=record.machine_part.get_frequency_in_days())

                ServiceSchedule.objects.create(
                    machine_part=record.machine_part,
                    scheduled_date=next_date,
                    assigned_technician=record.technician
                )
                messages.success(request, f'Next service scheduled for {next_date.strftime("%B %d, %Y")}!')

            return redirect('service:record_detail', pk=record.pk)
    else:
        form = ServiceRecordForm()

    context = {
        'form': form,
        'title': 'Create Service Record',
        'submit_text': 'Create Record',
    }
    return render(request, 'pages/service/record_form.html', context)


def service_record_edit(request, pk):
    """Edit an existing service record"""
    record = get_object_or_404(ServiceRecord, pk=pk)

    if request.method == 'POST':
        form = ServiceRecordForm(request.POST, instance=record)
        if form.is_valid():
            record = form.save()
            messages.success(request, f'Service record updated successfully!')
            return redirect('service:record_detail', pk=record.pk)
    else:
        form = ServiceRecordForm(instance=record)

    context = {
        'form': form,
        'record': record,
        'title': f'Edit Service Record',
        'submit_text': 'Update Record',
    }
    return render(request, 'pages/service/record_form.html', context)
