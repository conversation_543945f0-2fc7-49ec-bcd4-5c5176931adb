{% extends 'base/base.html' %}
{% load static %}

{% block title %}Inventory Management Dashboard{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 mb-0">Inventory Management</h1>
                <div class="btn-group" role="group">
                    <a href="{% url 'inventory:multi_part_order_create' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Multi-Part Order
                    </a>
                    <a href="{% url 'inventory:order_create' %}" class="btn btn-outline-primary">
                        <i class="fas fa-plus"></i> Single Part Order
                    </a>
                    <a href="{% url 'inventory:adjustment_create' %}" class="btn btn-outline-warning">
                        <i class="fas fa-adjust"></i> Inventory Adjustment
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ total_parts }}</h4>
                            <p class="mb-0">Total Parts</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-boxes fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{% url 'parts:list' %}" class="text-white text-decoration-none">
                        View All Parts <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ low_stock_count }}</h4>
                            <p class="mb-0">Low Stock</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{% url 'parts:list' %}?stock_status=low" class="text-white text-decoration-none">
                        View Low Stock <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ adequate_stock_count }}</h4>
                            <p class="mb-0">Adequate Stock</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{% url 'parts:list' %}?stock_status=adequate" class="text-white text-decoration-none">
                        View Adequate Stock <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
        
        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0">{{ overstocked_count }}</h4>
                            <p class="mb-0">Overstocked</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-warehouse fa-2x"></i>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{% url 'parts:list' %}?stock_status=overstocked" class="text-white text-decoration-none">
                        View Overstocked <i class="fas fa-arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <a href="{% url 'inventory:order_list' %}" class="btn btn-outline-primary w-100 mb-2">
                                <i class="fas fa-list"></i><br>
                                View All Orders
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'inventory:document_list' %}" class="btn btn-outline-info w-100 mb-2">
                                <i class="fas fa-file-alt"></i><br>
                                Order Documents
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'inventory:adjustment_list' %}" class="btn btn-outline-warning w-100 mb-2">
                                <i class="fas fa-adjust"></i><br>
                                Inventory Adjustments
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{% url 'parts:create' %}" class="btn btn-outline-success w-100 mb-2">
                                <i class="fas fa-plus"></i><br>
                                Add New Part
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Row -->
    <div class="row">
        <!-- Low Stock Parts -->
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-exclamation-triangle text-danger"></i> Low Stock Alert
                    </h5>
                    <a href="{% url 'parts:list' %}?stock_status=low" class="btn btn-sm btn-outline-danger">
                        View All
                    </a>
                </div>
                <div class="card-body">
                    {% if low_stock_parts %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Part Number</th>
                                        <th>Current Stock</th>
                                        <th>Min Stock</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for part in low_stock_parts %}
                                    <tr>
                                        <td>
                                            <a href="{% url 'parts:detail' part.pk %}">{{ part.part_no }}</a>
                                        </td>
                                        <td>
                                            <span class="badge bg-danger">{{ part.current_stock }}</span>
                                        </td>
                                        <td>{{ part.minimum_stock }}</td>
                                        <td>
                                            <a href="{% url 'inventory:order_create' %}?part={{ part.pk }}" 
                                               class="btn btn-sm btn-primary">Order</a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                            <p class="text-muted">All parts have adequate stock levels!</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Orders -->
        <div class="col-xl-6">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-shopping-cart"></i> Recent Orders
                    </h5>
                    <a href="{% url 'inventory:order_list' %}" class="btn btn-sm btn-outline-primary">
                        View All
                    </a>
                </div>
                <div class="card-body">
                    {% if recent_orders %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Part</th>
                                        <th>Quantity</th>
                                        <th>Date</th>
                                        <th>Supplier</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for order in recent_orders %}
                                    <tr>
                                        <td>
                                            <a href="{% url 'parts:detail' order.part.pk %}">
                                                {{ order.part.part_no|truncatechars:15 }}
                                            </a>
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ order.quantity }}</span>
                                        </td>
                                        <td>{{ order.order_date|date:"M d" }}</td>
                                        <td>{{ order.supplier|truncatechars:15|default:"-" }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-shopping-cart fa-2x text-muted mb-2"></i>
                            <p class="text-muted">No recent orders found.</p>
                            <a href="{% url 'inventory:order_create' %}" class="btn btn-primary btn-sm">
                                Create First Order
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Adjustments -->
    {% if recent_adjustments %}
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-adjust"></i> Recent Inventory Adjustments
                    </h5>
                    <a href="{% url 'inventory:adjustment_list' %}" class="btn btn-sm btn-outline-warning">
                        View All
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Part</th>
                                    <th>Type</th>
                                    <th>Quantity</th>
                                    <th>Reason</th>
                                    <th>Date</th>
                                    <th>Created By</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for adjustment in recent_adjustments %}
                                <tr>
                                    <td>
                                        <a href="{% url 'parts:detail' adjustment.part.pk %}">
                                            {{ adjustment.part.part_no|truncatechars:15 }}
                                        </a>
                                    </td>
                                    <td>
                                        {% if adjustment.adjustment_type == 'addition' %}
                                            <span class="badge bg-success">Addition</span>
                                        {% else %}
                                            <span class="badge bg-danger">Deduction</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if adjustment.adjustment_type == 'addition' %}
                                            <span class="text-success">+{{ adjustment.quantity }}</span>
                                        {% else %}
                                            <span class="text-danger">-{{ adjustment.quantity }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ adjustment.get_reason_display|truncatechars:15 }}</td>
                                    <td>{{ adjustment.adjustment_date|date:"M d" }}</td>
                                    <td>{{ adjustment.created_by|truncatechars:15 }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_css %}
<style>
    .card {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border: 1px solid rgba(0, 0, 0, 0.125);
    }
    
    .card-footer {
        background-color: rgba(0, 0, 0, 0.03);
        border-top: 1px solid rgba(0, 0, 0, 0.125);
    }
    
    .btn-group .btn {
        margin-right: 0.5rem;
    }
    
    .btn-group .btn:last-child {
        margin-right: 0;
    }
    
    .table th {
        border-top: none;
        font-weight: 600;
        color: #495057;
    }
    
    .badge {
        font-size: 0.75rem;
    }
</style>
{% endblock %}
