{% extends 'base/base.html' %}
{% load static %}

{% block title %}Part Orders{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Part Orders</h5>
                    <div class="btn-group" role="group">
                        <a href="{% url 'inventory:multi_part_order_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Multi-Part Order
                        </a>
                        <a href="{% url 'inventory:order_create' %}" class="btn btn-outline-primary">
                            <i class="fas fa-plus"></i> Single Order
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Search Form -->
                    <form method="get" class="mb-4">
                        <div class="row g-3">
                            <div class="col-md-3">
                                {{ search_form.part_no.label_tag }}
                                {{ search_form.part_no }}
                            </div>
                            <div class="col-md-3">
                                {{ search_form.supplier.label_tag }}
                                {{ search_form.supplier }}
                            </div>
                            <div class="col-md-2">
                                {{ search_form.date_from.label_tag }}
                                {{ search_form.date_from }}
                            </div>
                            <div class="col-md-2">
                                {{ search_form.date_to.label_tag }}
                                {{ search_form.date_to }}
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-outline-primary me-2">
                                    <i class="fas fa-search"></i> Search
                                </button>
                                <a href="{% url 'inventory:order_list' %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- Results -->
                    {% if page_obj %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Part Number</th>
                                        <th>Description</th>
                                        <th>Quantity</th>
                                        <th>Order Date</th>
                                        <th>Supplier</th>
                                        <th>Document</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for order in page_obj %}
                                    <tr>
                                        <td>
                                            <a href="{% url 'parts:detail' order.part.pk %}" class="text-decoration-none">
                                                <strong>{{ order.part.part_no }}</strong>
                                            </a>
                                        </td>
                                        <td>{{ order.part.description|truncatechars:40 }}</td>
                                        <td>
                                            <span class="badge bg-secondary">{{ order.quantity }}</span>
                                        </td>
                                        <td>{{ order.order_date|date:"M d, Y" }}</td>
                                        <td>
                                            {% if order.supplier %}
                                                {{ order.supplier|truncatechars:20 }}
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if order.order_reference_document %}
                                                <a href="{% url 'inventory:document_detail' order.order_reference_document.pk %}" 
                                                   class="btn btn-sm btn-outline-info" title="{{ order.order_reference_document.document_name }}">
                                                    <i class="fas fa-file-alt"></i>
                                                </a>
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{% url 'inventory:order_edit' order.pk %}" 
                                                   class="btn btn-outline-primary" title="Edit Order">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{% url 'parts:detail' order.part.pk %}" 
                                                   class="btn btn-outline-info" title="View Part">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if page_obj.has_other_pages %}
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1{% if request.GET.part_no %}&part_no={{ request.GET.part_no }}{% endif %}{% if request.GET.supplier %}&supplier={{ request.GET.supplier }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}">First</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.part_no %}&part_no={{ request.GET.part_no }}{% endif %}{% if request.GET.supplier %}&supplier={{ request.GET.supplier }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}">Previous</a>
                                    </li>
                                {% endif %}

                                <li class="page-item active">
                                    <span class="page-link">
                                        Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                                    </span>
                                </li>

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.part_no %}&part_no={{ request.GET.part_no }}{% endif %}{% if request.GET.supplier %}&supplier={{ request.GET.supplier }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}">Next</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.part_no %}&part_no={{ request.GET.part_no }}{% endif %}{% if request.GET.supplier %}&supplier={{ request.GET.supplier }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}">Last</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}

                        <!-- Results Summary -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <small class="text-muted">
                                Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ total_count }} orders
                            </small>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Orders Found</h5>
                            <p class="text-muted">No part orders match your search criteria.</p>
                            <a href="{% url 'inventory:multi_part_order_create' %}" class="btn btn-primary me-2">
                                <i class="fas fa-plus"></i> Create Multi-Part Order
                            </a>
                            <a href="{% url 'inventory:order_create' %}" class="btn btn-outline-primary">
                                <i class="fas fa-plus"></i> Create Single Order
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .table td {
        vertical-align: middle;
    }
    
    .badge {
        font-size: 0.875rem;
    }
    
    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
    }
</style>
{% endblock %}
