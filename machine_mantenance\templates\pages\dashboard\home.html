{% extends 'base/base.html' %}
{% load static %}

{% block title %}Dashboard - {{ block.super }}{% endblock %}

{% block page_header %}
<div class="header">
    <h1 class="header-title">
        Maintenance Dashboard
    </h1>
    <p class="header-subtitle">System overview and upcoming maintenance activities</p>
</div>
{% endblock %}

{% block content %}
<!-- Summary Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ total_machines }}</h4>
                        <p class="mb-0">Total Machines</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-industry fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="{% url 'machines:list' %}" class="text-white text-decoration-none">
                    View Machines <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ low_stock_count }}</h4>
                        <p class="mb-0">Low Stock Parts</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="{% url 'parts:list' %}?stock_status=low" class="text-white text-decoration-none">
                    View Low Stock <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ overdue_schedules }}</h4>
                        <p class="mb-0">Overdue Services</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="{% url 'service:schedule_list' %}" class="text-white text-decoration-none">
                    View Schedules <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 class="mb-0">{{ pending_tickets }}</h4>
                        <p class="mb-0">Pending Tickets</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-ticket-alt fa-2x"></i>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <a href="{% url 'service:ticket_list' %}" class="text-white text-decoration-none">
                    View Tickets <i class="fas fa-arrow-right"></i>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Main Content Row -->
<div class="row">
    <!-- Upcoming Service Schedules -->
    <div class="col-12 col-lg-6 d-flex">
        <div class="card flex-fill">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calendar-week text-primary"></i> Upcoming Service Schedules
                </h5>
                <a href="{% url 'service:schedule_list' %}" class="btn btn-sm btn-outline-primary">
                    View All
                </a>
            </div>
            <div class="card-body p-0">
                {% if upcoming_schedules %}
                    <div class="table-responsive">
                        <table class="table table-sm table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Machine Part</th>
                                    <th>Scheduled Date</th>
                                    <th>Technician</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for schedule in upcoming_schedules %}
                                <tr>
                                    <td>
                                        <a href="{% url 'service:schedule_edit' schedule.pk %}" class="text-decoration-none">
                                            <strong>{{ schedule.machine_part.machine.name }}</strong><br>
                                            <small class="text-muted">{{ schedule.machine_part.part.part_no }}</small>
                                        </a>
                                    </td>
                                    <td>
                                        {% if schedule.scheduled_date < today %}
                                            <span class="badge bg-danger">{{ schedule.scheduled_date|date:"M d" }}</span>
                                        {% elif schedule.scheduled_date == today %}
                                            <span class="badge bg-warning">Today</span>
                                        {% else %}
                                            <span class="badge bg-success">{{ schedule.scheduled_date|date:"M d" }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if schedule.assigned_technician %}
                                            {{ schedule.assigned_technician.name|truncatechars:15 }}
                                        {% else %}
                                            <span class="text-muted">Unassigned</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-check fa-2x text-muted mb-2"></i>
                        <p class="text-muted">No upcoming schedules</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Low Stock Parts -->
    <div class="col-12 col-lg-6 d-flex">
        <div class="card flex-fill">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-exclamation-triangle text-danger"></i> Low Stock Alert
                </h5>
                <a href="{% url 'parts:list' %}?stock_status=low" class="btn btn-sm btn-outline-danger">
                    View All
                </a>
            </div>
            <div class="card-body p-0">
                {% if low_stock_parts %}
                    <div class="table-responsive">
                        <table class="table table-sm table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Part Number</th>
                                    <th>Current Stock</th>
                                    <th>Min Stock</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for part in low_stock_parts %}
                                <tr>
                                    <td>
                                        <a href="{% url 'parts:detail' part.pk %}" class="text-decoration-none">
                                            <strong>{{ part.part_no }}</strong><br>
                                            <small class="text-muted">{{ part.description|truncatechars:20 }}</small>
                                        </a>
                                    </td>
                                    <td>
                                        <span class="badge bg-danger">{{ part.current_stock }}</span>
                                    </td>
                                    <td>{{ part.minimum_stock }}</td>
                                    <td>
                                        <a href="{% url 'inventory:order_create' %}?part={{ part.pk }}"
                                           class="btn btn-sm btn-primary">Order</a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                        <p class="text-muted">All parts have adequate stock!</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Second Row -->
<div class="row mt-4">
    <!-- Upcoming Service Tickets -->
    <div class="col-12 col-lg-6 d-flex">
        <div class="card flex-fill">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-ticket-alt text-info"></i> Upcoming Service Tickets
                </h5>
                <a href="{% url 'service:ticket_list' %}" class="btn btn-sm btn-outline-info">
                    View All
                </a>
            </div>
            <div class="card-body p-0">
                {% if upcoming_tickets %}
                    <div class="table-responsive">
                        <table class="table table-sm table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Machine</th>
                                    <th>Scheduled Date</th>
                                    <th>Status</th>
                                    <th>Technician</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for ticket in upcoming_tickets %}
                                <tr>
                                    <td>
                                        <a href="{% url 'service:ticket_detail' ticket.pk %}" class="text-decoration-none">
                                            <strong>{{ ticket.machine.name }}</strong><br>
                                            <small class="text-muted">{{ ticket.machine.tag_number }}</small>
                                        </a>
                                    </td>
                                    <td>
                                        {% if ticket.service_date < today %}
                                            <span class="badge bg-danger">{{ ticket.service_date|date:"M d" }}</span>
                                        {% elif ticket.service_date == today %}
                                            <span class="badge bg-warning">Today</span>
                                        {% else %}
                                            <span class="badge bg-success">{{ ticket.scheduled_date|date:"M d" }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if ticket.status == 'pending' %}
                                            <span class="badge bg-warning">Pending</span>
                                        {% elif ticket.status == 'in_progress' %}
                                            <span class="badge bg-info">In Progress</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ ticket.get_status_display }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if ticket.assigned_technician %}
                                            {{ ticket.assigned_technician.name|truncatechars:15 }}
                                        {% else %}
                                            <span class="text-muted">Unassigned</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-clipboard-check fa-2x text-muted mb-2"></i>
                        <p class="text-muted">No upcoming tickets</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Recent Service Records -->
    <div class="col-12 col-lg-6 d-flex">
        <div class="card flex-fill">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-wrench text-success"></i> Recent Service Records
                </h5>
                <a href="{% url 'service:record_list' %}" class="btn btn-sm btn-outline-success">
                    View All
                </a>
            </div>
            <div class="card-body p-0">
                {% if recent_service_records %}
                    <div class="table-responsive">
                        <table class="table table-sm table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Machine Part</th>
                                    <th>Service Date</th>
                                    <th>Technician</th>
                                    <th>Parts Used</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for record in recent_service_records %}
                                <tr>
                                    <td>
                                        <a href="{% url 'service:record_detail' record.pk %}" class="text-decoration-none">
                                            <strong>{{ record.machine_part.machine.name }}</strong><br>
                                            <small class="text-muted">{{ record.machine_part.part.part_no }}</small>
                                        </a>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ record.service_date|date:"M d" }}</span>
                                    </td>
                                    <td>{{ record.technician.name|truncatechars:15 }}</td>
                                    <td>
                                        <span class="badge bg-info">{{ record.number_of_parts_replaced }}</span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-tools fa-2x text-muted mb-2"></i>
                        <p class="text-muted">No recent service records</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_css %}
<style>
    .card {
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border: 1px solid rgba(0, 0, 0, 0.125);
    }

    .card-footer {
        background-color: rgba(0, 0, 0, 0.03);
        border-top: 1px solid rgba(0, 0, 0, 0.125);
    }

    .table th {
        border-top: none;
        font-weight: 600;
        color: #495057;
        font-size: 0.875rem;
    }

    .table td {
        vertical-align: middle;
        font-size: 0.875rem;
    }

    .badge {
        font-size: 0.75rem;
    }

    .card-title i {
        margin-right: 0.5rem;
    }
</style>
{% endblock %}

{% block extra_js %}
{% endblock %}
