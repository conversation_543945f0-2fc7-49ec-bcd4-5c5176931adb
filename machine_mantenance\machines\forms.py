"""
Forms for machine management in the maintenance system.
"""
from django import forms
from django.forms import formset_factory, BaseFormSet
from .models import Machine, Part, PartOrder, MachinePart, MachineSystem, OrderDocument, InventoryAdjustment
from forms.base import SparkCRUDForm, SparkSearchForm
from forms.mixins import TimestampFieldsMixin, LocationFieldsMixin, ServiceFrequencyMixin


class MachineSystemForm(TimestampFieldsMixin, SparkCRUDForm):
    """Form for creating and editing machine systems"""

    class Meta:
        model = MachineSystem
        fields = ['name']
        labels = {
            'name': 'System Name',
        }

    # Override description field for custom styling
    description = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': 'Enter system description'
        }),
        required=False,
        label='Description'
    )


class MachineForm(TimestampFieldsMixin, LocationFieldsMixin, SparkCRUDForm):
    """Form for creating and editing machines"""
    
    class Meta:
        model = Machine
        fields = ['system', 'name', 'serial_number', 'tag_number', 'description', 'location']
        labels = {
            'system': 'Machine System',
            'name': 'Machine Name',
            'serial_number': 'Serial Number',
            'tag_number': 'Tag Number',
            'description': 'Description',
            'location': 'Location',
        }
    
    # Override description field for custom styling
    description = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 4,
            'placeholder': 'Enter detailed machine description'
        }),
        required=False,
        label='Description'
    )


class PartForm(TimestampFieldsMixin, SparkCRUDForm):
    """Form for creating and editing parts"""
    
    class Meta:
        model = Part
        fields = ['part_no', 'description', 'minimum_stock']
        labels = {
            'part_no': 'Part Number',
            'description': 'Description',
            'minimum_stock': 'Minimum Stock Level',
        }
    
    # Override fields for custom styling
    description = forms.CharField(
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter part description'
        }),
        required=False,
        label='Description'
    )
    
    minimum_stock = forms.IntegerField(
        min_value=0,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter minimum stock level'
        }),
        label='Minimum Stock Level',
        help_text='Minimum quantity that should be kept in stock'
    )


class OrderDocumentForm(SparkCRUDForm):
    """Form for uploading order documents"""

    class Meta:
        model = OrderDocument
        fields = ['document_name', 'document_file', 'document_type', 'notes']
        labels = {
            'document_name': 'Document Name',
            'document_file': 'Document File',
            'document_type': 'Document Type',
            'notes': 'Notes',
        }

    notes = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': 'Optional notes about this document...'
        }),
        required=False,
        label='Notes'
    )


class PartOrderForm(SparkCRUDForm):
    """Form for creating part orders"""

    class Meta:
        model = PartOrder
        fields = ['part', 'quantity', 'order_date', 'order_reference_document', 'supplier']
        labels = {
            'part': 'Part',
            'quantity': 'Quantity Ordered',
            'order_date': 'Order Date',
            'order_reference_document': 'Reference Document',
            'supplier': 'Supplier',
        }
        widgets = {
            'order_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'supplier': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter supplier name'
            }),
        }

    quantity = forms.IntegerField(
        min_value=1,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter quantity'
        }),
        label='Quantity Ordered'
    )


class InventoryAdjustmentForm(SparkCRUDForm):
    """Form for creating inventory adjustments"""

    class Meta:
        model = InventoryAdjustment
        fields = ['part', 'adjustment_type', 'quantity', 'reason', 'description', 'adjustment_date', 'created_by']
        labels = {
            'part': 'Part',
            'adjustment_type': 'Adjustment Type',
            'quantity': 'Quantity',
            'reason': 'Reason',
            'description': 'Description',
            'adjustment_date': 'Adjustment Date',
            'created_by': 'Created By',
        }
        widgets = {
            'adjustment_date': forms.DateInput(attrs={
                'class': 'form-control',
                'type': 'date'
            }),
            'created_by': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': 'Enter your name'
            }),
        }

    quantity = forms.IntegerField(
        min_value=1,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter quantity'
        }),
        label='Quantity'
    )

    description = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 4,
            'placeholder': 'Provide detailed explanation for this adjustment...'
        }),
        label='Description'
    )


class MachinePartForm(ServiceFrequencyMixin, SparkCRUDForm):
    """Form for adding parts to machines with service instructions"""
    
    class Meta:
        model = MachinePart
        fields = ['machine', 'part', 'running_hours', 'service_frequency', 'service_frequency_unit', 'service_instructions']
        labels = {
            'machine': 'Machine',
            'part': 'Part',
            'running_hours': 'Running Hours',
            'service_frequency': 'Service Frequency',
            'service_frequency_unit': 'Frequency Unit',
            'service_instructions': 'Service Instructions',
        }
    
    # Override servive frequency for custom styling
    service_frequency = forms.IntegerField(
        min_value=1,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Frequency number'
        }),
        label='Service Frequency'
    )
    # Override service instructions for custom styling
    service_instructions = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 5,
            'placeholder': 'Enter detailed service instructions...'
        }),
        label='Service Instructions'
    )


class MachineSearchForm(SparkSearchForm):
    """Form for searching machines"""
    
    name = forms.CharField(
        label='Machine Name',
        required=False
    )
    
    tag_number = forms.CharField(
        label='Tag Number',
        required=False
    )
    
    location = forms.CharField(
        label='Location',
        required=False
    )


class PartSearchForm(SparkSearchForm):
    """Form for searching parts"""

    part_no = forms.CharField(
        label='Part Number',
        required=False
    )

    stock_status = forms.ChoiceField(
        choices=[
            ('', 'All Parts'),
            ('low', 'Low Stock'),
            ('adequate', 'Adequate Stock'),
            ('overstocked', 'Overstocked'),
        ],
        required=False,
        label='Stock Status',
        widget=forms.Select(attrs={'class': 'form-select'})
    )


class MachineSystemSearchForm(SparkSearchForm):
    """Form for searching machine systems"""

    name = forms.CharField(
        label='System Name',
        required=False
    )


class InventorySearchForm(SparkSearchForm):
    """Form for searching inventory adjustments"""

    part_no = forms.CharField(
        label='Part Number',
        required=False
    )

    adjustment_type = forms.ChoiceField(
        choices=[
            ('', 'All Types'),
            ('addition', 'Addition'),
            ('deduction', 'Deduction'),
        ],
        required=False,
        label='Adjustment Type',
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    reason = forms.ChoiceField(
        choices=[('', 'All Reasons')] + InventoryAdjustment.REASON_CHOICES,
        required=False,
        label='Reason',
        widget=forms.Select(attrs={'class': 'form-select'})
    )

    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label='Date From'
    )

    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label='Date To'
    )


class OrderSearchForm(SparkSearchForm):
    """Form for searching part orders"""

    part_no = forms.CharField(
        label='Part Number',
        required=False
    )

    supplier = forms.CharField(
        label='Supplier',
        required=False
    )

    date_from = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label='Order Date From'
    )

    date_to = forms.DateField(
        required=False,
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label='Order Date To'
    )


# Multi-Part Order Forms
class MultiPartOrderItemForm(forms.Form):
    """Form for individual items in a multi-part order"""

    part = forms.ModelChoiceField(
        queryset=Part.objects.all(),
        widget=forms.Select(attrs={
            'class': 'form-select part-select',
            'data-placeholder': 'Select a part...'
        }),
        label='Part',
        empty_label='Select a part...'
    )

    quantity = forms.IntegerField(
        min_value=1,
        widget=forms.NumberInput(attrs={
            'class': 'form-control quantity-input',
            'placeholder': 'Qty',
            'min': '1'
        }),
        label='Quantity'
    )


    def clean_quantity(self):
        quantity = self.cleaned_data.get('quantity')
        if quantity and quantity < 1:
            raise forms.ValidationError('Quantity must be at least 1.')
        return quantity


class BaseMultiPartOrderFormSet(BaseFormSet):
    """Base formset for multi-part orders with validation"""

    def clean(self):
        """Validate the formset"""
        if any(self.errors):
            return

        parts = []
        for form in self.forms:
            if form.cleaned_data and not form.cleaned_data.get('DELETE', False):
                part = form.cleaned_data.get('part')
                if part:
                    if part in parts:
                        raise forms.ValidationError('Each part can only be added once per order.')
                    parts.append(part)

        if not parts:
            raise forms.ValidationError('At least one part must be added to the order.')


# Create the formset
MultiPartOrderFormSet = formset_factory(
    MultiPartOrderItemForm,
    formset=BaseMultiPartOrderFormSet,
    extra=3,  # Show 3 empty forms by default
    can_delete=True,
    min_num=1,
    validate_min=True
)


class MultiPartOrderForm(SparkCRUDForm):
    """Form for creating multi-part orders with document"""

    class Meta:
        model = OrderDocument
        fields = ['document_name', 'document_file', 'document_type', 'notes']
        labels = {
            'document_name': 'Order Document Name',
            'document_file': 'Order Document File',
            'document_type': 'Document Type',
            'notes': 'Order Notes',
        }

    # Additional fields for the order
    order_date = forms.DateField(
        widget=forms.DateInput(attrs={
            'class': 'form-control',
            'type': 'date'
        }),
        label='Order Date',
        help_text='Date when the order was placed'
    )

    supplier = forms.CharField(
        max_length=200,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Enter supplier name'
        }),
        label='Supplier',
        help_text='Supplier or vendor name for this order'
    )

    notes = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'rows': 3,
            'placeholder': 'Optional notes about this order...'
        }),
        required=False,
        label='Order Notes'
    )
