# Generated by Django 5.2.1 on 2025-09-07 10:22

import django.core.validators
import django.db.models.deletion
import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('machines', '0005_alter_part_description'),
    ]

    operations = [
        # Create the OrderDocument model first
        migrations.CreateModel(
            name='OrderDocument',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('document_name', models.CharField(max_length=200, verbose_name='Document Name')),
                ('document_file', models.FileField(help_text='Upload purchase order, invoice, or receipt', upload_to='order_documents/', verbose_name='Document File')),
                ('document_type', models.CharField(choices=[('purchase_order', 'Purchase Order'), ('invoice', 'Invoice'), ('receipt', 'Receipt'), ('other', 'Other')], default='purchase_order', max_length=50, verbose_name='Document Type')),
                ('upload_date', models.DateTimeField(auto_now_add=True)),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
            ],
            options={
                'verbose_name': 'Order Document',
                'verbose_name_plural': 'Order Documents',
                'ordering': ['-upload_date'],
            },
        ),
        # Add new fields to PartOrder
        migrations.AddField(
            model_name='partorder',
            name='supplier',
            field=models.CharField(blank=True, help_text='Supplier or vendor name', max_length=200, verbose_name='Supplier'),
        ),
        migrations.AddField(
            model_name='partorder',
            name='unit_cost',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Cost per unit (optional)', max_digits=10, null=True, verbose_name='Unit Cost'),
        ),
        # Create the InventoryAdjustment model
        migrations.CreateModel(
            name='InventoryAdjustment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('adjustment_type', models.CharField(choices=[('addition', 'Addition'), ('deduction', 'Deduction')], max_length=20, verbose_name='Adjustment Type')),
                ('quantity', models.IntegerField(validators=[django.core.validators.MinValueValidator(1)], verbose_name='Quantity')),
                ('reason', models.CharField(choices=[('damage', 'Damage'), ('theft', 'Theft'), ('requisition', 'Requisition'), ('found', 'Found/Discovered'), ('correction', 'Inventory Correction'), ('expired', 'Expired/Obsolete'), ('other', 'Other')], max_length=50, verbose_name='Reason')),
                ('description', models.TextField(help_text='Detailed explanation of the adjustment', verbose_name='Description')),
                ('adjustment_date', models.DateField(default=django.utils.timezone.now, verbose_name='Adjustment Date')),
                ('created_by', models.CharField(help_text='Name of person making the adjustment', max_length=100, verbose_name='Created By')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('part', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='inventory_adjustments', to='machines.part', verbose_name='Part')),
            ],
            options={
                'verbose_name': 'Inventory Adjustment',
                'verbose_name_plural': 'Inventory Adjustments',
                'ordering': ['-adjustment_date', '-created_at'],
            },
        ),
    ]
