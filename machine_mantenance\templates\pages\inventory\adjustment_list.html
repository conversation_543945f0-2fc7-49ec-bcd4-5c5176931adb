{% extends 'base/base.html' %}
{% load static %}

{% block title %}Inventory Adjustments{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Inventory Adjustments</h5>
                    <a href="{% url 'inventory:adjustment_create' %}" class="btn btn-warning">
                        <i class="fas fa-adjust"></i> New Adjustment
                    </a>
                </div>
                <div class="card-body">
                    <!-- Search Form -->
                    <form method="get" class="mb-4">
                        <div class="row g-3">
                            <div class="col-md-2">
                                {{ search_form.part_no.label_tag }}
                                {{ search_form.part_no }}
                            </div>
                            <div class="col-md-2">
                                {{ search_form.adjustment_type.label_tag }}
                                {{ search_form.adjustment_type }}
                            </div>
                            <div class="col-md-2">
                                {{ search_form.reason.label_tag }}
                                {{ search_form.reason }}
                            </div>
                            <div class="col-md-2">
                                {{ search_form.date_from.label_tag }}
                                {{ search_form.date_from }}
                            </div>
                            <div class="col-md-2">
                                {{ search_form.date_to.label_tag }}
                                {{ search_form.date_to }}
                            </div>
                            <div class="col-md-2 d-flex align-items-end">
                                <button type="submit" class="btn btn-outline-primary me-2">
                                    <i class="fas fa-search"></i> Search
                                </button>
                                <a href="{% url 'inventory:adjustment_list' %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i> Clear
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- Results -->
                    {% if page_obj %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Part Number</th>
                                        <th>Type</th>
                                        <th>Quantity</th>
                                        <th>Reason</th>
                                        <th>Date</th>
                                        <th>Created By</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for adjustment in page_obj %}
                                    <tr>
                                        <td>
                                            <a href="{% url 'parts:detail' adjustment.part.pk %}" class="text-decoration-none">
                                                <strong>{{ adjustment.part.part_no }}</strong>
                                            </a>
                                        </td>
                                        <td>
                                            {% if adjustment.adjustment_type == 'addition' %}
                                                <span class="badge bg-success">
                                                    <i class="fas fa-plus"></i> Addition
                                                </span>
                                            {% else %}
                                                <span class="badge bg-danger">
                                                    <i class="fas fa-minus"></i> Deduction
                                                </span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if adjustment.adjustment_type == 'addition' %}
                                                <span class="text-success fw-bold">+{{ adjustment.quantity }}</span>
                                            {% else %}
                                                <span class="text-danger fw-bold">-{{ adjustment.quantity }}</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge bg-secondary">{{ adjustment.get_reason_display }}</span>
                                        </td>
                                        <td>{{ adjustment.adjustment_date|date:"M d, Y" }}</td>
                                        <td>{{ adjustment.created_by }}</td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{% url 'inventory:adjustment_detail' adjustment.pk %}" 
                                                   class="btn btn-outline-primary" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{% url 'parts:detail' adjustment.part.pk %}" 
                                                   class="btn btn-outline-info" title="View Part">
                                                    <i class="fas fa-box"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        {% if page_obj.has_other_pages %}
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1{% if request.GET.part_no %}&part_no={{ request.GET.part_no }}{% endif %}{% if request.GET.adjustment_type %}&adjustment_type={{ request.GET.adjustment_type }}{% endif %}{% if request.GET.reason %}&reason={{ request.GET.reason }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}">First</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.part_no %}&part_no={{ request.GET.part_no }}{% endif %}{% if request.GET.adjustment_type %}&adjustment_type={{ request.GET.adjustment_type }}{% endif %}{% if request.GET.reason %}&reason={{ request.GET.reason }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}">Previous</a>
                                    </li>
                                {% endif %}

                                <li class="page-item active">
                                    <span class="page-link">
                                        Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                                    </span>
                                </li>

                                {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.part_no %}&part_no={{ request.GET.part_no }}{% endif %}{% if request.GET.adjustment_type %}&adjustment_type={{ request.GET.adjustment_type }}{% endif %}{% if request.GET.reason %}&reason={{ request.GET.reason }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}">Next</a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.part_no %}&part_no={{ request.GET.part_no }}{% endif %}{% if request.GET.adjustment_type %}&adjustment_type={{ request.GET.adjustment_type }}{% endif %}{% if request.GET.reason %}&reason={{ request.GET.reason }}{% endif %}{% if request.GET.date_from %}&date_from={{ request.GET.date_from }}{% endif %}{% if request.GET.date_to %}&date_to={{ request.GET.date_to }}{% endif %}">Last</a>
                                    </li>
                                {% endif %}
                            </ul>
                        </nav>
                        {% endif %}

                        <!-- Results Summary -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <small class="text-muted">
                                Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ total_count }} adjustments
                            </small>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-adjust fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Adjustments Found</h5>
                            <p class="text-muted">No inventory adjustments match your search criteria.</p>
                            <a href="{% url 'inventory:adjustment_create' %}" class="btn btn-warning">
                                <i class="fas fa-plus"></i> Create First Adjustment
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .table td {
        vertical-align: middle;
    }
    
    .badge {
        font-size: 0.875rem;
    }
    
    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
    }
    
    .fw-bold {
        font-weight: 600 !important;
    }
</style>
{% endblock %}
