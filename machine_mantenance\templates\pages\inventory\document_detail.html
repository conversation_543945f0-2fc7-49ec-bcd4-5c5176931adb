{% extends 'base/base.html' %}
{% load static %}

{% block title %}Order Document: {{ document.document_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Document Information Card -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-file-alt"></i> {{ document.document_name }}
                    </h5>
                    <div>
                        <a href="{% url 'inventory:document_list' %}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to Documents
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Document Type:</strong></td>
                                    <td>
                                        <span class="badge bg-primary">{{ document.get_document_type_display }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Upload Date:</strong></td>
                                    <td>{{ document.upload_date|date:"F d, Y g:i A" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Total Items:</strong></td>
                                    <td>
                                        <span class="badge bg-info">{{ total_items }} part{{ total_items|pluralize }}</span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            {% if document.document_file %}
                            <div class="mb-3">
                                <strong>Document File:</strong><br>
                                <a href="{{ document.document_file.url }}" target="_blank" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-download"></i> Download Document
                                </a>
                            </div>
                            {% endif %}
                            {% if document.notes %}
                            <div>
                                <strong>Notes:</strong><br>
                                <p class="text-muted">{{ document.notes|linebreaks }}</p>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Part Orders Card -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-boxes"></i> Ordered Parts
                    </h5>
                </div>
                <div class="card-body">
                    {% if part_orders %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Part Number</th>
                                        <th>Description</th>
                                        <th>Quantity</th>
                                        <th>Order Date</th>
                                        <th>Supplier</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for order in part_orders %}
                                    <tr>
                                        <td>
                                            <a href="{% url 'parts:detail' order.part.pk %}" class="text-decoration-none">
                                                <strong>{{ order.part.part_no }}</strong>
                                            </a>
                                        </td>
                                        <td>{{ order.part.description|truncatechars:50 }}</td>
                                        <td>
                                            <span class="badge bg-secondary">{{ order.quantity }}</span>
                                        </td>
                                        <td>{{ order.order_date|date:"M d, Y" }}</td>
                                        <td>
                                            {% if order.supplier %}
                                                {{ order.supplier|truncatechars:30 }}
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{% url 'inventory:order_edit' order.pk %}" 
                                                   class="btn btn-outline-primary" title="Edit Order">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="{% url 'parts:detail' order.part.pk %}" 
                                                   class="btn btn-outline-info" title="View Part">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- Summary Row -->
                        <div class="row mt-3">
                            <div class="col-md-6 offset-md-6">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <span><strong>Total Items:</strong></span>
                                            <span>{{ total_items }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Parts Ordered</h5>
                            <p class="text-muted">This document doesn't have any associated part orders yet.</p>
                            <a href="{% url 'inventory:order_create' %}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add Part Order
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .table td {
        vertical-align: middle;
    }
    
    .badge {
        font-size: 0.875rem;
    }
    
    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
    }
    
    .card.bg-light {
        border: 1px solid #dee2e6;
    }
</style>
{% endblock %}
