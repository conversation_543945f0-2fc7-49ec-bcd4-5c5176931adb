# Generated by Django 5.2.1 on 2025-09-07 10:25

import django.db.models.deletion
from django.db import migrations, models


def migrate_order_documents(apps, schema_editor):
    """
    Migrate existing FileField data to OrderDocument instances and update foreign keys.
    """
    PartOrder = apps.get_model('machines', 'PartOrder')
    OrderDocument = apps.get_model('machines', 'OrderDocument')
    
    # Get all PartOrder instances that have file references
    part_orders_with_files = PartOrder.objects.exclude(order_reference_document='').exclude(order_reference_document__isnull=True)
    
    for part_order in part_orders_with_files:
        if part_order.order_reference_document:
            # Create an OrderDocument instance for each file
            order_doc = OrderDocument.objects.create(
                document_name=f"Order Document for {part_order.part.part_no} - {part_order.order_date}",
                document_file=part_order.order_reference_document,
                document_type='purchase_order',
                notes=f"Migrated from PartOrder #{part_order.id}"
            )
            # Store the OrderDocument ID in a temporary field (we'll add this field)
            part_order.temp_order_doc_id = order_doc.id
            part_order.save()


def reverse_migrate_order_documents(apps, schema_editor):
    """
    Reverse migration - convert OrderDocument instances back to file references.
    """
    PartOrder = apps.get_model('machines', 'PartOrder')
    OrderDocument = apps.get_model('machines', 'OrderDocument')
    
    # Get all PartOrder instances that have temp_order_doc_id
    part_orders = PartOrder.objects.exclude(temp_order_doc_id__isnull=True)
    
    for part_order in part_orders:
        try:
            order_doc = OrderDocument.objects.get(id=part_order.temp_order_doc_id)
            part_order.order_reference_document = order_doc.document_file
            part_order.save()
        except OrderDocument.DoesNotExist:
            pass


class Migration(migrations.Migration):

    dependencies = [
        ('machines', '0006_add_orderdocument_and_fields'),
    ]

    operations = [
        # Add a temporary field to store OrderDocument IDs during migration
        migrations.AddField(
            model_name='partorder',
            name='temp_order_doc_id',
            field=models.IntegerField(null=True, blank=True),
        ),
        # Migrate existing file data to OrderDocument instances
        migrations.RunPython(
            migrate_order_documents,
            reverse_migrate_order_documents,
        ),
        # Remove the old FileField
        migrations.RemoveField(
            model_name='partorder',
            name='order_reference_document',
        ),
        # Add the new ForeignKey field
        migrations.AddField(
            model_name='partorder',
            name='order_reference_document',
            field=models.ForeignKey(
                blank=True,
                help_text='Link to purchase order or receipt document',
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name='part_orders',
                to='machines.orderdocument',
                verbose_name='Reference Document'
            ),
        ),
    ]
