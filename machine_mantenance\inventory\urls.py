"""
URL patterns for inventory management.
"""
from django.urls import path
from machines import views

app_name = 'inventory'

urlpatterns = [
    # Inventory Management URLs
    path('', views.inventory_dashboard, name='inventory_dashboard'),

    # Order Management URLs
    path('orders/', views.order_list, name='order_list'),
    path('orders/create/', views.order_create, name='order_create'),
    path('orders/<int:pk>/edit/', views.order_edit, name='order_edit'),
    path('orders/multi-part/create/', views.multi_part_order_create, name='multi_part_order_create'),

    # Document Management URLs
    path('documents/', views.document_list, name='document_list'),
    path('documents/create/', views.document_create, name='document_create'),
    path('documents/<int:pk>/', views.document_detail, name='document_detail'),

    # Inventory Adjustment URLs
    path('adjustments/', views.adjustment_list, name='adjustment_list'),
    path('adjustments/create/', views.adjustment_create, name='adjustment_create'),
    path('adjustments/<int:pk>/', views.adjustment_detail, name='adjustment_detail'),
]
