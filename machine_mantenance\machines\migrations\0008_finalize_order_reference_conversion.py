# Generated by Django 5.2.1 on 2025-09-07 10:30

from django.db import migrations


def set_foreign_key_relationships(apps, schema_editor):
    """
    Set the foreign key relationships using the temporary IDs.
    """
    PartOrder = apps.get_model('machines', 'PartOrder')
    OrderDocument = apps.get_model('machines', 'OrderDocument')
    
    # Update PartOrder instances to point to the correct OrderDocument
    part_orders = PartOrder.objects.exclude(temp_order_doc_id__isnull=True)
    
    for part_order in part_orders:
        try:
            order_doc = OrderDocument.objects.get(id=part_order.temp_order_doc_id)
            part_order.order_reference_document = order_doc
            part_order.save()
        except OrderDocument.DoesNotExist:
            pass


def reverse_set_foreign_key_relationships(apps, schema_editor):
    """
    Reverse operation - clear foreign key relationships and restore temp IDs.
    """
    PartOrder = apps.get_model('machines', 'PartOrder')
    
    part_orders = PartOrder.objects.exclude(order_reference_document__isnull=True)
    
    for part_order in part_orders:
        part_order.temp_order_doc_id = part_order.order_reference_document.id
        part_order.order_reference_document = None
        part_order.save()


class Migration(migrations.Migration):

    dependencies = [
        ('machines', '0007_convert_order_reference_document'),
    ]

    operations = [
        # Set the foreign key relationships
        migrations.RunPython(
            set_foreign_key_relationships,
            reverse_set_foreign_key_relationships,
        ),
        # Remove the temporary field
        migrations.RemoveField(
            model_name='partorder',
            name='temp_order_doc_id',
        ),
    ]
