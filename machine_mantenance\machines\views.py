from django.shortcuts import render, get_object_or_404, redirect
from django.contrib import messages
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone
from .models import Machine, Part, PartOrder, MachinePart, MachineSystem, OrderDocument, InventoryAdjustment
from .forms import (
    MachineForm, PartForm, PartOrderForm, MachinePartForm,
    MachineSearchForm, PartSearchForm, MachineSystemForm, MachineSystemSearchForm,
    OrderDocumentForm, InventoryAdjustmentForm, InventorySearchForm, OrderSearchForm,
    MultiPartOrderForm, MultiPartOrderFormSet
)


# Machine System Views
def system_list(request):
    """List all machine systems with search functionality"""
    search_form = MachineSystemSearchForm(request.GET)
    systems = MachineSystem.objects.all()

    if search_form.is_valid():
        name = search_form.cleaned_data.get('name')
        if name:
            systems = systems.filter(name__icontains=name)

    paginator = Paginator(systems, 25)  # Show 25 systems per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_form': search_form,
        'total_count': systems.count(),
    }
    return render(request, 'pages/machine-systems/system_list.html', context)


def system_detail(request, pk):
    """Display system details with associated machines"""
    system = get_object_or_404(MachineSystem, pk=pk)
    machines = system.machines.all()

    context = {
        'system': system,
        'machines': machines,
    }
    return render(request, 'pages/machine-systems/system_detail.html', context)


def system_create(request):
    """Create a new machine system"""
    if request.method == 'POST':
        form = MachineSystemForm(request.POST)
        if form.is_valid():
            system = form.save()
            messages.success(request, f'Machine system "{system.name}" created successfully!')
            return redirect('systems:detail', pk=system.pk)
    else:
        form = MachineSystemForm()

    context = {
        'form': form,
        'title': 'Add New Machine System',
        'submit_text': 'Create System',
    }
    return render(request, 'pages/machine-systems/system_form.html', context)


def system_edit(request, pk):
    """Edit an existing machine system"""
    system = get_object_or_404(MachineSystem, pk=pk)

    if request.method == 'POST':
        form = MachineSystemForm(request.POST, instance=system)
        if form.is_valid():
            system = form.save()
            messages.success(request, f'Machine system "{system.name}" updated successfully!')
            return redirect('systems:detail', pk=system.pk)
    else:
        form = MachineSystemForm(instance=system)

    context = {
        'form': form,
        'system': system,
        'title': f'Edit System: {system.name}',
        'submit_text': 'Update System',
    }
    return render(request, 'pages/machine-systems/system_form.html', context)


# Machine Views
def machine_list(request):
    """List all machines with search functionality"""
    search_form = MachineSearchForm(request.GET)
    machines = Machine.objects.all()

    if search_form.is_valid():
        name = search_form.cleaned_data.get('name')
        tag_number = search_form.cleaned_data.get('tag_number')
        location = search_form.cleaned_data.get('location')

        if name:
            machines = machines.filter(name__icontains=name)
        if tag_number:
            machines = machines.filter(tag_number__icontains=tag_number)
        if location:
            machines = machines.filter(location__icontains=location)

    paginator = Paginator(machines, 25)  # Show 25 machines per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_form': search_form,
        'total_count': machines.count(),
    }
    return render(request, 'pages/machines/machine_list.html', context)


def machine_detail(request, pk):
    """Display machine details with associated parts"""
    machine = get_object_or_404(Machine, pk=pk)
    machine_parts = machine.machine_parts.all()

    context = {
        'machine': machine,
        'machine_parts': machine_parts,
    }
    return render(request, 'pages/machines/machine_detail.html', context)


def machine_create(request):
    """Create a new machine"""
    # Check if system is pre-selected from URL parameter
    system_pk = request.GET.get('system')
    system = None
    if system_pk:
        try:
            system = get_object_or_404(MachineSystem, pk=system_pk)
        except:
            system = None

    if request.method == 'POST':
        form = MachineForm(request.POST)
        if form.is_valid():
            machine = form.save()
            messages.success(request, f'Machine "{machine.name}" created successfully!')
            return redirect('machines:detail', pk=machine.pk)
    else:
        initial = {'system': system} if system else {}
        form = MachineForm(initial=initial)

    context = {
        'form': form,
        'system': system,
        'title': 'Add New Machine',
        'submit_text': 'Create Machine',
    }
    return render(request, 'pages/machines/machine_form.html', context)


def machine_edit(request, pk):
    """Edit an existing machine"""
    machine = get_object_or_404(Machine, pk=pk)

    if request.method == 'POST':
        form = MachineForm(request.POST, instance=machine)
        if form.is_valid():
            machine = form.save()
            messages.success(request, f'Machine "{machine.name}" updated successfully!')
            return redirect('machines:detail', pk=machine.pk)
    else:
        form = MachineForm(instance=machine)

    context = {
        'form': form,
        'machine': machine,
        'title': f'Edit Machine: {machine.name}',
        'submit_text': 'Update Machine',
    }
    return render(request, 'pages/machines/machine_form.html', context)


def machine_delete(request, pk):
    """Delete a machine"""
    machine = get_object_or_404(Machine, pk=pk)

    if request.method == 'POST':
        machine_name = machine.name
        machine.delete()
        messages.success(request, f'Machine "{machine_name}" deleted successfully!')
        return redirect('machines:list')

    context = {
        'machine': machine,
    }
    return render(request, 'pages/machines/machine_confirm_delete.html', context)


# Part Views
def part_list(request):
    """List all parts with search functionality and stock status"""
    search_form = PartSearchForm(request.GET)
    parts = Part.objects.all()

    if search_form.is_valid():
        part_no = search_form.cleaned_data.get('part_no')
        stock_status = search_form.cleaned_data.get('stock_status')

        if part_no:
            parts = parts.filter(part_no__icontains=part_no)

        # Filter by stock status if specified
        if stock_status:
            filtered_parts = []
            for part in parts:
                if part.stock_status == stock_status:
                    filtered_parts.append(part.pk)
            parts = parts.filter(pk__in=filtered_parts)

    paginator = Paginator(parts, 25)  # Show 25 parts per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_form': search_form,
        'total_count': parts.count(),
    }
    return render(request, 'pages/parts/part_list.html', context)


def part_detail(request, pk):
    """Display part details with stock information and usage history"""
    part = get_object_or_404(Part, pk=pk)
    part_orders = part.part_orders.all()[:10]  # Recent orders
    machine_parts = part.part_machines.all()  # Machines using this part

    context = {
        'part': part,
        'part_orders': part_orders,
        'machine_parts': machine_parts,
        'current_stock': part.current_stock,
        'stock_status': part.stock_status,
    }
    return render(request, 'pages/parts/part_detail.html', context)


def part_create(request):
    """Create a new part"""
    if request.method == 'POST':
        form = PartForm(request.POST)
        if form.is_valid():
            part = form.save()
            messages.success(request, f'Part "{part.part_no}" created successfully!')
            return redirect('parts:detail', pk=part.pk)
    else:
        form = PartForm()

    context = {
        'form': form,
        'title': 'Add New Part',
        'submit_text': 'Create Part',
    }
    return render(request, 'pages/parts/part_form.html', context)


def part_edit(request, pk):
    """Edit an existing part"""
    part = get_object_or_404(Part, pk=pk)

    if request.method == 'POST':
        form = PartForm(request.POST, instance=part)
        if form.is_valid():
            part = form.save()
            messages.success(request, f'Part "{part.part_no}" updated successfully!')
            return redirect('parts:detail', pk=part.pk)
    else:
        form = PartForm(instance=part)

    context = {
        'form': form,
        'part': part,
        'title': f'Edit Part: {part.part_no}',
        'submit_text': 'Update Part',
    }
    return render(request, 'pages/parts/part_form.html', context)


# Part Order Views
def part_order_create(request, part_pk=None):
    """Create a new part order"""
    part = None
    if part_pk:
        part = get_object_or_404(Part, pk=part_pk)

    if request.method == 'POST':
        form = PartOrderForm(request.POST, request.FILES)
        if form.is_valid():
            part_order = form.save()
            messages.success(request, f'Part order for "{part_order.part.part_no}" created successfully!')
            return redirect('parts:detail', pk=part_order.part.pk)
    else:
        initial = {'part': part} if part else {}
        form = PartOrderForm(initial=initial)

    context = {
        'form': form,
        'part': part,
        'title': 'Add Part Order',
        'submit_text': 'Create Order',
    }
    return render(request, 'pages/parts/part_order_form.html', context)


# Machine Part Views
def machine_part_create(request, machine_pk=None):
    """Add a part to a machine with service instructions"""
    machine = None
    if machine_pk:
        machine = get_object_or_404(Machine, pk=machine_pk)

    if request.method == 'POST':
        form = MachinePartForm(request.POST)
        if form.is_valid():
            machine_part = form.save()
            messages.success(request, f'Part "{machine_part.part.part_no}" added to machine "{machine_part.machine.name}" successfully!')

            # Inform user about automatic schedule creation
            next_service_date = machine_part.get_next_service_date()
            messages.info(request, f'Initial service automatically scheduled for {next_service_date.strftime("%B %d, %Y")}.')

            # Check if user clicked "Save and Add Another"
            if 'save_and_add_another' in request.POST:
                # Redirect back to the form with the same machine pre-selected
                return redirect('machines:add_part_to_machine', machine_pk=machine_part.machine.pk)
            else:
                # Regular save - redirect to machine detail
                return redirect('machines:detail', pk=machine_part.machine.pk)
    else:
        initial = {'machine': machine} if machine else {}
        form = MachinePartForm(initial=initial)

    context = {
        'form': form,
        'machine': machine,
        'title': 'Add Part to Machine',
        'submit_text': 'Add Part',
    }
    return render(request, 'pages/machines/machine_part_form.html', context)


def machine_part_edit(request, pk):
    """Edit machine part service instructions"""
    machine_part = get_object_or_404(MachinePart, pk=pk)

    if request.method == 'POST':
        # Store original frequency to detect changes
        original_frequency = machine_part.service_frequency
        original_unit = machine_part.service_frequency_unit

        form = MachinePartForm(request.POST, instance=machine_part)
        if form.is_valid():
            machine_part = form.save()
            messages.success(request, f'Machine part "{machine_part}" updated successfully!')

            # Check if service frequency changed
            if (machine_part.service_frequency != original_frequency or
                machine_part.service_frequency_unit != original_unit):

                # Update future service schedules if frequency changed
                from service.models import ServiceSchedule
                future_schedules = ServiceSchedule.objects.filter(
                    machine_part=machine_part,
                    scheduled_date__gt=timezone.now().date()
                )

                if future_schedules.exists():
                    messages.info(request, f'Service frequency updated. Consider reviewing {future_schedules.count()} future service schedule(s).')

            return redirect('machines:detail', pk=machine_part.machine.pk)
    else:
        form = MachinePartForm(instance=machine_part)

    context = {
        'form': form,
        'machine_part': machine_part,
        'title': f'Edit Machine Part: {machine_part}',
        'submit_text': 'Update Part',
    }
    return render(request, 'pages/machines/machine_part_form.html', context)


# Inventory Management Views
def order_list(request):
    """List all part orders with search functionality"""
    search_form = OrderSearchForm(request.GET)
    orders = PartOrder.objects.all().select_related('part', 'order_reference_document')

    if search_form.is_valid():
        part_no = search_form.cleaned_data.get('part_no')
        supplier = search_form.cleaned_data.get('supplier')
        date_from = search_form.cleaned_data.get('date_from')
        date_to = search_form.cleaned_data.get('date_to')

        if part_no:
            orders = orders.filter(part__part_no__icontains=part_no)
        if supplier:
            orders = orders.filter(supplier__icontains=supplier)
        if date_from:
            orders = orders.filter(order_date__gte=date_from)
        if date_to:
            orders = orders.filter(order_date__lte=date_to)

    paginator = Paginator(orders, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_form': search_form,
        'total_count': orders.count(),
    }
    return render(request, 'pages/inventory/order_list.html', context)


def order_create(request):
    """Create a new part order"""
    if request.method == 'POST':
        form = PartOrderForm(request.POST)
        if form.is_valid():
            order = form.save()
            messages.success(request, f'Order for {order.quantity} units of "{order.part.part_no}" created successfully!')
            return redirect('machines:order_list')
    else:
        form = PartOrderForm()

    context = {
        'form': form,
        'title': 'Create Part Order',
        'submit_text': 'Create Order',
    }
    return render(request, 'pages/inventory/order_form.html', context)


def order_edit(request, pk):
    """Edit an existing part order"""
    order = get_object_or_404(PartOrder, pk=pk)

    if request.method == 'POST':
        form = PartOrderForm(request.POST, instance=order)
        if form.is_valid():
            order = form.save()
            messages.success(request, f'Order for "{order.part.part_no}" updated successfully!')
            return redirect('machines:order_list')
    else:
        form = PartOrderForm(instance=order)

    context = {
        'form': form,
        'order': order,
        'title': f'Edit Order: {order.part.part_no}',
        'submit_text': 'Update Order',
    }
    return render(request, 'pages/inventory/order_form.html', context)


def document_list(request):
    """List all order documents"""
    documents = OrderDocument.objects.all()

    paginator = Paginator(documents, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'total_count': documents.count(),
    }
    return render(request, 'pages/inventory/document_list.html', context)


def document_create(request):
    """Upload a new order document"""
    if request.method == 'POST':
        form = OrderDocumentForm(request.POST, request.FILES)
        if form.is_valid():
            document = form.save()
            messages.success(request, f'Document "{document.document_name}" uploaded successfully!')
            return redirect('machines:document_list')
    else:
        form = OrderDocumentForm()

    context = {
        'form': form,
        'title': 'Upload Order Document',
        'submit_text': 'Upload Document',
    }
    return render(request, 'pages/inventory/document_form.html', context)


def adjustment_list(request):
    """List all inventory adjustments with search functionality"""
    search_form = InventorySearchForm(request.GET)
    adjustments = InventoryAdjustment.objects.all().select_related('part')

    if search_form.is_valid():
        part_no = search_form.cleaned_data.get('part_no')
        adjustment_type = search_form.cleaned_data.get('adjustment_type')
        reason = search_form.cleaned_data.get('reason')
        date_from = search_form.cleaned_data.get('date_from')
        date_to = search_form.cleaned_data.get('date_to')

        if part_no:
            adjustments = adjustments.filter(part__part_no__icontains=part_no)
        if adjustment_type:
            adjustments = adjustments.filter(adjustment_type=adjustment_type)
        if reason:
            adjustments = adjustments.filter(reason=reason)
        if date_from:
            adjustments = adjustments.filter(adjustment_date__gte=date_from)
        if date_to:
            adjustments = adjustments.filter(adjustment_date__lte=date_to)

    paginator = Paginator(adjustments, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'search_form': search_form,
        'total_count': adjustments.count(),
    }
    return render(request, 'pages/inventory/adjustment_list.html', context)


def adjustment_create(request):
    """Create a new inventory adjustment"""
    if request.method == 'POST':
        form = InventoryAdjustmentForm(request.POST)
        if form.is_valid():
            adjustment = form.save()
            sign = '+' if adjustment.adjustment_type == 'addition' else '-'
            messages.success(request, f'Inventory adjustment created: {sign}{adjustment.quantity} units of "{adjustment.part.part_no}"')
            return redirect('machines:adjustment_list')
    else:
        form = InventoryAdjustmentForm()

    context = {
        'form': form,
        'title': 'Create Inventory Adjustment',
        'submit_text': 'Create Adjustment',
    }
    return render(request, 'pages/inventory/adjustment_form.html', context)


def adjustment_detail(request, pk):
    """Display inventory adjustment details"""
    adjustment = get_object_or_404(InventoryAdjustment, pk=pk)

    context = {
        'adjustment': adjustment,
    }
    return render(request, 'pages/inventory/adjustment_detail.html', context)


# Multi-Part Order Views
def multi_part_order_create(request):
    """Create a multi-part order with multiple parts linked to one document"""
    if request.method == 'POST':
        order_form = MultiPartOrderForm(request.POST, request.FILES)
        formset = MultiPartOrderFormSet(request.POST)

        if order_form.is_valid() and formset.is_valid():
            # Create the OrderDocument first
            order_document = order_form.save()

            # Get common order data
            order_date = order_form.cleaned_data['order_date']
            supplier = order_form.cleaned_data['supplier']

            # Create PartOrder instances for each part
            part_orders_created = 0
            for form in formset:
                if form.cleaned_data and not form.cleaned_data.get('DELETE', False):
                    part = form.cleaned_data['part']
                    quantity = form.cleaned_data['quantity']

                    PartOrder.objects.create(
                        part=part,
                        quantity=quantity,
                        order_date=order_date,
                        order_reference_document=order_document,
                        supplier=supplier
                    )
                    part_orders_created += 1

            messages.success(
                request,
                f'Multi-part order "{order_document.document_name}" created successfully with {part_orders_created} parts!'
            )
            return redirect('machines:order_list')
    else:
        order_form = MultiPartOrderForm()
        formset = MultiPartOrderFormSet()

    context = {
        'order_form': order_form,
        'formset': formset,
        'title': 'Create Multi-Part Order',
        'submit_text': 'Create Order',
    }
    return render(request, 'pages/inventory/multi_part_order_form.html', context)


def document_detail(request, pk):
    """Display order document details with associated part orders"""
    document = get_object_or_404(OrderDocument, pk=pk)
    part_orders = document.part_orders.all().select_related('part')

    # Calculate totals
    total_items = part_orders.count()

    context = {
        'document': document,
        'part_orders': part_orders,
        'total_items': total_items,
    }
    return render(request, 'pages/inventory/document_detail.html', context)
