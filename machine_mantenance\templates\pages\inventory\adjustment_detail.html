{% extends 'base/base.html' %}
{% load static %}

{% block title %}Inventory Adjustment Details{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-adjust"></i> Inventory Adjustment Details
                    </h5>
                    <div>
                        <a href="{% url 'inventory:adjustment_list' %}" class="btn btn-outline-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> Back to Adjustments
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Part:</strong></td>
                                    <td>
                                        <a href="{% url 'parts:detail' adjustment.part.pk %}" class="text-decoration-none">
                                            {{ adjustment.part.part_no }} - {{ adjustment.part.description }}
                                        </a>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Adjustment Type:</strong></td>
                                    <td>
                                        {% if adjustment.adjustment_type == 'addition' %}
                                            <span class="badge bg-success">
                                                <i class="fas fa-plus"></i> Addition
                                            </span>
                                        {% else %}
                                            <span class="badge bg-danger">
                                                <i class="fas fa-minus"></i> Deduction
                                            </span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Quantity:</strong></td>
                                    <td>
                                        {% if adjustment.adjustment_type == 'addition' %}
                                            <span class="text-success fw-bold fs-5">+{{ adjustment.quantity }}</span>
                                        {% else %}
                                            <span class="text-danger fw-bold fs-5">-{{ adjustment.quantity }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Reason:</strong></td>
                                    <td>
                                        <span class="badge bg-secondary">{{ adjustment.get_reason_display }}</span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Adjustment Date:</strong></td>
                                    <td>{{ adjustment.adjustment_date|date:"F d, Y" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Created By:</strong></td>
                                    <td>{{ adjustment.created_by }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Created On:</strong></td>
                                    <td>{{ adjustment.created_at|date:"F d, Y g:i A" }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Current Part Stock:</strong></td>
                                    <td>
                                        <span class="badge 
                                            {% if adjustment.part.stock_status == 'low' %}bg-danger
                                            {% elif adjustment.part.stock_status == 'adequate' %}bg-success
                                            {% else %}bg-info{% endif %}">
                                            {{ adjustment.part.current_stock }} units
                                        </span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">Description</h6>
                            <div class="card bg-light">
                                <div class="card-body">
                                    <p class="mb-0">{{ adjustment.description|linebreaks }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <h6 class="text-primary mb-3">Quick Actions</h6>
                            <div class="btn-group" role="group">
                                <a href="{% url 'parts:detail' adjustment.part.pk %}" class="btn btn-outline-info">
                                    <i class="fas fa-box"></i> View Part Details
                                </a>
                                <a href="{% url 'inventory:adjustment_create' %}" class="btn btn-outline-warning">
                                    <i class="fas fa-plus"></i> Create Another Adjustment
                                </a>
                                <a href="{% url 'inventory:order_create' %}?part={{ adjustment.part.pk }}" class="btn btn-outline-primary">
                                    <i class="fas fa-shopping-cart"></i> Order This Part
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .table td {
        vertical-align: middle;
        padding: 0.75rem 0.5rem;
    }
    
    .badge {
        font-size: 0.875rem;
    }
    
    .fw-bold {
        font-weight: 600 !important;
    }
    
    .fs-5 {
        font-size: 1.25rem !important;
    }
    
    .card.bg-light {
        border: 1px solid #dee2e6;
    }
    
    .btn-group .btn {
        margin-right: 0.5rem;
    }
    
    .btn-group .btn:last-child {
        margin-right: 0;
    }
</style>
{% endblock %}
